#!/usr/bin/env python3
"""
Simple Gait Regression Training Script

A simplified version to test the training pipeline with CPU/GPU compatibility fixes.
"""

import os
import sys
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# Import our modules
from gait_regression_model import (
    load_and_preprocess_data, GaitVideoDataset, 
    VideoFeatureExtractor, GaitRegressionModel
)

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SimpleGaitTrainer:
    """Simplified trainer with better dtype handling."""
    
    def __init__(self, model, feature_extractor, device="cpu", learning_rate=1e-3):
        self.device = device
        self.feature_extractor = feature_extractor
        
        # Ensure model is on the right device and dtype
        if device == "cpu":
            self.model = model.float().to(device)
        else:
            self.model = model.to(device)
            
        self.optimizer = optim.Adam(self.model.parameters(), lr=learning_rate)
        self.criterion = nn.MSELoss()
        
        self.train_losses = []
        self.val_losses = []
        
        logger.info(f"Initialized trainer on {device}")
    
    def extract_features_batch(self, videos):
        """Extract features with proper dtype handling."""
        features_list = []
        
        for video in videos:
            features = self.feature_extractor.extract_features(video)
            
            # Convert to CPU and float32 if needed
            if self.device == "cpu":
                features = features.cpu().float()
            
            features_list.append(features.unsqueeze(0))
        
        batch_features = torch.cat(features_list, dim=0)
        return batch_features.to(self.device)
    
    def train_epoch(self, dataloader):
        """Train for one epoch."""
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        for videos, targets in tqdm(dataloader, desc="Training"):
            try:
                # Extract features
                with torch.no_grad():
                    features = self.extract_features_batch(videos)
                
                # Move targets to device and ensure float32
                targets = targets.float().to(self.device)
                
                # Forward pass
                self.optimizer.zero_grad()
                predictions = self.model(features)
                loss = self.criterion(predictions, targets)
                
                # Backward pass
                loss.backward()
                self.optimizer.step()
                
                total_loss += loss.item()
                num_batches += 1
                
            except Exception as e:
                logger.error(f"Error in training batch: {e}")
                continue
        
        if num_batches > 0:
            avg_loss = total_loss / num_batches
            self.train_losses.append(avg_loss)
            return avg_loss
        else:
            return float('inf')
    
    def validate(self, dataloader):
        """Validate the model."""
        self.model.eval()
        total_loss = 0.0
        all_predictions = []
        all_targets = []
        num_batches = 0
        
        with torch.no_grad():
            for videos, targets in tqdm(dataloader, desc="Validating"):
                try:
                    # Extract features
                    features = self.extract_features_batch(videos)
                    
                    # Move targets to device
                    targets = targets.float().to(self.device)
                    
                    # Forward pass
                    predictions = self.model(features)
                    loss = self.criterion(predictions, targets)
                    
                    total_loss += loss.item()
                    num_batches += 1
                    
                    # Store for metrics
                    all_predictions.append(predictions.cpu().numpy())
                    all_targets.append(targets.cpu().numpy())
                    
                except Exception as e:
                    logger.error(f"Error in validation batch: {e}")
                    continue
        
        if num_batches > 0:
            avg_loss = total_loss / num_batches
            self.val_losses.append(avg_loss)
            
            # Calculate metrics
            if all_predictions and all_targets:
                predictions = np.concatenate(all_predictions, axis=0)
                targets = np.concatenate(all_targets, axis=0)
                
                r2 = r2_score(targets, predictions)
                mae = mean_absolute_error(targets, predictions)
                
                return avg_loss, {'r2': r2, 'mae': mae}
            else:
                return avg_loss, {'r2': 0.0, 'mae': float('inf')}
        else:
            return float('inf'), {'r2': 0.0, 'mae': float('inf')}
    
    def fit(self, train_loader, val_loader, epochs=5):
        """Train the model."""
        logger.info(f"Starting training for {epochs} epochs...")
        
        best_val_loss = float('inf')
        
        for epoch in range(epochs):
            # Train
            train_loss = self.train_epoch(train_loader)
            
            # Validate
            val_loss, metrics = self.validate(val_loader)
            
            # Log progress
            logger.info(f"Epoch {epoch+1}/{epochs}: "
                       f"Train Loss: {train_loss:.4f}, "
                       f"Val Loss: {val_loss:.4f}, "
                       f"Val R²: {metrics['r2']:.4f}")
            
            # Save best model
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                torch.save(self.model.state_dict(), "simple_best_model.pth")
                logger.info(f"Saved best model with validation loss: {val_loss:.4f}")
        
        return {
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'best_val_loss': best_val_loss
        }


def main():
    """Main training function."""
    logger.info("Starting Simple Gait Regression Training")
    
    # Configuration
    config = {
        'device': 'cpu',  # Use CPU to avoid dtype issues
        'batch_size': 1,
        'epochs': 3,
        'learning_rate': 1e-3,
        'subset_size': 50,  # Use small subset for testing
        'test_size': 0.3,
        'val_size': 0.2
    }
    
    logger.info(f"Configuration: {config}")
    
    try:
        # Load data
        logger.info("Loading data...")
        video_paths, gait_params, param_names = load_and_preprocess_data()
        
        # Use subset for testing
        subset_size = min(config['subset_size'], len(video_paths))
        video_paths = video_paths[:subset_size]
        gait_params = gait_params[:subset_size]
        
        logger.info(f"Using subset of {subset_size} samples")
        
        # Create data splits
        train_val_paths, test_paths, train_val_params, test_params = train_test_split(
            video_paths, gait_params, test_size=config['test_size'], random_state=42
        )
        
        train_paths, val_paths, train_params, val_params = train_test_split(
            train_val_paths, train_val_params, test_size=config['val_size'], random_state=42
        )
        
        logger.info(f"Data split: Train={len(train_paths)}, Val={len(val_paths)}, Test={len(test_paths)}")
        
        # Create datasets and loaders
        train_dataset = GaitVideoDataset(train_paths, train_params)
        val_dataset = GaitVideoDataset(val_paths, val_params)
        
        train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], shuffle=False)
        
        # Initialize feature extractor (CPU version)
        logger.info("Initializing feature extractor...")
        feature_extractor = VideoFeatureExtractor(
            model_name="Cosmos-1.0-Tokenizer-CV8x8x8",
            device=config['device'],
            dtype="float32",  # Use float32 for CPU
            temporal_window=17  # Smaller window
        )
        
        # Initialize model
        logger.info("Initializing regression model...")
        model = GaitRegressionModel(
            input_channels=16,
            num_gait_params=len(param_names),
            hidden_dims=[128, 64]  # Smaller model
        )
        
        # Initialize trainer
        trainer = SimpleGaitTrainer(
            model=model,
            feature_extractor=feature_extractor,
            device=config['device'],
            learning_rate=config['learning_rate']
        )
        
        # Train
        logger.info("Starting training...")
        history = trainer.fit(
            train_loader=train_loader,
            val_loader=val_loader,
            epochs=config['epochs']
        )
        
        # Plot results
        plt.figure(figsize=(10, 4))
        
        plt.subplot(1, 2, 1)
        plt.plot(history['train_losses'], label='Train Loss')
        plt.plot(history['val_losses'], label='Val Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Training History')
        plt.legend()
        plt.grid(True)
        
        plt.subplot(1, 2, 2)
        plt.plot(history['train_losses'], label='Train Loss')
        plt.plot(history['val_losses'], label='Val Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss (Log)')
        plt.title('Training History (Log Scale)')
        plt.yscale('log')
        plt.legend()
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('simple_training_results.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        logger.info("Training completed successfully!")
        logger.info(f"Best validation loss: {history['best_val_loss']:.4f}")
        logger.info("Results saved to simple_training_results.png")
        
        return history
        
    except Exception as e:
        logger.error(f"Error in training: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    main()
