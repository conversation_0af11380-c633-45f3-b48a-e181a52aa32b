#!/usr/bin/env python3
"""
Test script for the Gait Parameter Regression Model

This script provides comprehensive testing for the gait regression pipeline,
including unit tests, integration tests, and performance validation.
"""

import os
import sys
import unittest
import numpy as np
import torch
import tempfile
import shutil
from unittest.mock import patch, MagicMock
import logging

# Import our modules
from gait_regression_model import (
    GaitVideoDataset, VideoFeatureExtractor, GaitRegressionModel,
    GaitRegressionTrainer, load_and_preprocess_data, create_data_loaders,
    plot_training_history, evaluate_model
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestGaitVideoDataset(unittest.TestCase):
    """Test cases for GaitVideoDataset class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.video_paths = ["test_video_1.avi", "test_video_2.avi"]
        self.gait_params = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], dtype=np.float32)
    
    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir)
    
    def test_dataset_initialization(self):
        """Test dataset initialization."""
        dataset = GaitVideoDataset(self.video_paths, self.gait_params, self.temp_dir)
        self.assertEqual(len(dataset), 2)
        self.assertEqual(dataset.video_paths, self.video_paths)
        np.testing.assert_array_equal(dataset.gait_params, self.gait_params)
    
    def test_dataset_length_mismatch(self):
        """Test dataset with mismatched lengths."""
        with self.assertRaises(AssertionError):
            GaitVideoDataset(["video1.avi"], self.gait_params, self.temp_dir)
    
    @patch('gait_regression_model.read_video')
    def test_dataset_getitem(self, mock_read_video):
        """Test dataset __getitem__ method."""
        # Mock video data
        mock_video = np.random.randint(0, 255, (16, 224, 224, 3), dtype=np.uint8)
        mock_read_video.return_value = mock_video
        
        dataset = GaitVideoDataset(self.video_paths, self.gait_params, self.temp_dir)
        video, params = dataset[0]
        
        np.testing.assert_array_equal(video, mock_video)
        np.testing.assert_array_equal(params, self.gait_params[0])


class TestVideoFeatureExtractor(unittest.TestCase):
    """Test cases for VideoFeatureExtractor class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.device = "cpu"  # Use CPU for testing
        self.dtype = "float32"
    
    @patch('gait_regression_model.CausalVideoTokenizer')
    @patch('os.path.exists')
    def test_feature_extractor_initialization(self, mock_exists, mock_tokenizer):
        """Test feature extractor initialization."""
        mock_exists.return_value = True
        mock_tokenizer_instance = MagicMock()
        mock_tokenizer.return_value = mock_tokenizer_instance
        
        extractor = VideoFeatureExtractor(
            model_name="test-model",
            device=self.device,
            dtype=self.dtype,
            temporal_window=16
        )
        
        self.assertEqual(extractor.device, self.device)
        self.assertEqual(extractor.dtype, self.dtype)
        self.assertEqual(extractor.temporal_window, 16)
    
    def test_feature_extractor_missing_checkpoint(self):
        """Test feature extractor with missing checkpoint."""
        with self.assertRaises(FileNotFoundError):
            VideoFeatureExtractor(
                model_name="nonexistent-model",
                device=self.device,
                dtype=self.dtype
            )


class TestGaitRegressionModel(unittest.TestCase):
    """Test cases for GaitRegressionModel class."""
    
    def test_model_initialization(self):
        """Test model initialization."""
        model = GaitRegressionModel(
            input_channels=16,
            num_gait_params=6,
            hidden_dims=[128, 64]
        )
        
        # Test model structure
        self.assertIsInstance(model.global_pool, torch.nn.AdaptiveAvgPool3d)
        self.assertIsInstance(model.regressor, torch.nn.Sequential)
    
    def test_model_forward_pass(self):
        """Test model forward pass."""
        model = GaitRegressionModel(
            input_channels=16,
            num_gait_params=6,
            hidden_dims=[128, 64]
        )
        
        # Create dummy input
        batch_size = 2
        input_features = torch.randn(batch_size, 16, 8, 16, 16)
        
        # Forward pass
        output = model(input_features)
        
        # Check output shape
        self.assertEqual(output.shape, (batch_size, 6))
    
    def test_model_different_configurations(self):
        """Test model with different configurations."""
        configs = [
            {'input_channels': 8, 'num_gait_params': 4, 'hidden_dims': [64]},
            {'input_channels': 32, 'num_gait_params': 10, 'hidden_dims': [256, 128, 64]},
        ]
        
        for config in configs:
            model = GaitRegressionModel(**config)
            batch_size = 1
            input_features = torch.randn(batch_size, config['input_channels'], 4, 8, 8)
            output = model(input_features)
            self.assertEqual(output.shape, (batch_size, config['num_gait_params']))


class TestDataPreprocessing(unittest.TestCase):
    """Test cases for data preprocessing functions."""
    
    @patch('pandas.read_excel')
    @patch('os.path.exists')
    def test_load_and_preprocess_data(self, mock_exists, mock_read_excel):
        """Test data loading and preprocessing."""
        # Mock DataFrame
        mock_df = MagicMock()
        mock_df.__len__.return_value = 100
        mock_df.columns = ['VideoFile', 'Velocity', 'Cadence', 'Stride_Len_L', 
                          'Stride_Len_R', 'Swing_Perc_L', 'Swing_Perc_R']
        mock_df.iterrows.return_value = [
            (0, {'VideoFile': '/path/to/video1.avi'}),
            (1, {'VideoFile': '/path/to/video2.avi'})
        ]
        mock_df.iloc.return_value = mock_df
        mock_df.__getitem__.return_value.values = np.array([[1, 2, 3, 4, 5, 6], [7, 8, 9, 10, 11, 12]])
        
        mock_read_excel.return_value = mock_df
        mock_exists.return_value = True
        
        # Test function
        video_paths, gait_params, param_names = load_and_preprocess_data(
            "dummy_path.xlsx", "dummy_video_dir"
        )
        
        self.assertIsInstance(video_paths, list)
        self.assertIsInstance(gait_params, np.ndarray)
        self.assertIsInstance(param_names, list)


class TestTrainingPipeline(unittest.TestCase):
    """Test cases for training pipeline."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.device = "cpu"
        self.model = GaitRegressionModel(input_channels=16, num_gait_params=6)
        
        # Mock feature extractor
        self.feature_extractor = MagicMock()
        self.feature_extractor.extract_features.return_value = torch.randn(16, 4, 8, 8)
    
    def test_trainer_initialization(self):
        """Test trainer initialization."""
        trainer = GaitRegressionTrainer(
            model=self.model,
            feature_extractor=self.feature_extractor,
            device=self.device
        )
        
        self.assertEqual(trainer.device, self.device)
        self.assertIsInstance(trainer.optimizer, torch.optim.Adam)
        self.assertIsInstance(trainer.criterion, torch.nn.MSELoss)
    
    def test_create_data_loaders(self):
        """Test data loader creation."""
        video_paths = [f"video_{i}.avi" for i in range(20)]
        gait_params = np.random.randn(20, 6).astype(np.float32)
        
        train_loader, val_loader, test_loader = create_data_loaders(
            video_paths, gait_params, test_size=0.2, val_size=0.2, batch_size=2
        )
        
        self.assertIsNotNone(train_loader)
        self.assertIsNotNone(val_loader)
        self.assertIsNotNone(test_loader)


class TestVisualization(unittest.TestCase):
    """Test cases for visualization functions."""
    
    def test_plot_training_history(self):
        """Test training history plotting."""
        history = {
            'train_losses': [1.0, 0.8, 0.6, 0.4],
            'val_losses': [1.2, 0.9, 0.7, 0.5]
        }
        
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            try:
                plot_training_history(history, tmp_file.name)
                self.assertTrue(os.path.exists(tmp_file.name))
            finally:
                os.unlink(tmp_file.name)


class IntegrationTest(unittest.TestCase):
    """Integration tests for the complete pipeline."""
    
    def setUp(self):
        """Set up integration test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        
        # Create dummy data
        self.video_paths = [f"video_{i}.avi" for i in range(10)]
        self.gait_params = np.random.randn(10, 6).astype(np.float32)
        
        # Create dummy video files
        for video_path in self.video_paths:
            full_path = os.path.join(self.temp_dir, video_path)
            with open(full_path, 'wb') as f:
                f.write(b'dummy video content')
    
    def tearDown(self):
        """Clean up integration test fixtures."""
        shutil.rmtree(self.temp_dir)
    
    @patch('gait_regression_model.read_video')
    def test_dataset_integration(self, mock_read_video):
        """Test dataset integration."""
        # Mock video reading
        mock_read_video.return_value = np.random.randint(0, 255, (16, 224, 224, 3), dtype=np.uint8)
        
        dataset = GaitVideoDataset(self.video_paths, self.gait_params, self.temp_dir)
        
        # Test dataset functionality
        self.assertEqual(len(dataset), 10)
        
        video, params = dataset[0]
        self.assertEqual(video.shape, (16, 224, 224, 3))
        self.assertEqual(params.shape, (6,))
    
    def test_model_pipeline_integration(self):
        """Test complete model pipeline integration."""
        # Create small model for testing
        model = GaitRegressionModel(
            input_channels=16,
            num_gait_params=6,
            hidden_dims=[32, 16]
        )
        
        # Test forward pass with different input sizes
        test_inputs = [
            torch.randn(1, 16, 4, 8, 8),
            torch.randn(2, 16, 8, 16, 16),
            torch.randn(1, 16, 2, 4, 4)
        ]
        
        for input_tensor in test_inputs:
            output = model(input_tensor)
            self.assertEqual(output.shape[0], input_tensor.shape[0])
            self.assertEqual(output.shape[1], 6)


def run_performance_test():
    """Run performance tests to validate model efficiency."""
    logger.info("Running performance tests...")
    
    # Test model inference speed
    model = GaitRegressionModel(input_channels=16, num_gait_params=6)
    model.eval()
    
    # Warm up
    dummy_input = torch.randn(1, 16, 8, 16, 16)
    with torch.no_grad():
        _ = model(dummy_input)
    
    # Time inference
    import time
    num_runs = 100
    start_time = time.time()
    
    with torch.no_grad():
        for _ in range(num_runs):
            _ = model(dummy_input)
    
    end_time = time.time()
    avg_inference_time = (end_time - start_time) / num_runs
    
    logger.info(f"Average inference time: {avg_inference_time*1000:.2f} ms")
    
    # Test memory usage
    import psutil
    process = psutil.Process()
    memory_before = process.memory_info().rss / 1024 / 1024  # MB
    
    # Create larger batch
    large_input = torch.randn(8, 16, 8, 16, 16)
    with torch.no_grad():
        _ = model(large_input)
    
    memory_after = process.memory_info().rss / 1024 / 1024  # MB
    memory_increase = memory_after - memory_before
    
    logger.info(f"Memory increase for batch size 8: {memory_increase:.2f} MB")


def main():
    """Main test runner."""
    logger.info("Starting comprehensive test suite for Gait Regression Model")
    
    # Run unit tests
    logger.info("Running unit tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run performance tests
    run_performance_test()
    
    logger.info("All tests completed!")


if __name__ == "__main__":
    main()
