#!/usr/bin/env python3
"""
Run evaluation on the trained comprehensive gait regression model.
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# Import our modules
from comprehensive_gait_regression import (
    load_and_preprocess_data, ComprehensiveGaitVideoDataset, 
    ComprehensiveGaitRegressionModel, comprehensive_evaluation,
    create_comprehensive_plots
)

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def main():
    """Run comprehensive evaluation."""
    logger.info("Starting Comprehensive Evaluation")
    
    # Configuration
    config = {
        'device': 'cuda' if torch.cuda.is_available() else 'cpu',
        'batch_size': 8,
        'subset_size': 500,
        'test_size': 0.2,
        'val_size': 0.2,
        'target_frames': 16,
        'target_size': (112, 112)
    }
    
    try:
        # Load data
        logger.info("Loading data...")
        video_paths, gait_params, param_names = load_and_preprocess_data()
        
        # Use same subset as training
        subset_size = min(config['subset_size'], len(video_paths))
        video_paths = video_paths[:subset_size]
        gait_params = gait_params[:subset_size]
        
        # Create same data splits as training
        train_val_paths, test_paths, train_val_params, test_params = train_test_split(
            video_paths, gait_params, test_size=config['test_size'], random_state=42
        )
        
        train_paths, val_paths, train_params, val_params = train_test_split(
            train_val_paths, train_val_params, test_size=config['val_size'], random_state=42
        )
        
        logger.info(f"Test set: {len(test_paths)} samples")
        
        # Load best model
        logger.info("Loading best model...")
        checkpoint = torch.load("comprehensive_best_model.pth", weights_only=False)
        
        model = ComprehensiveGaitRegressionModel(
            feature_dim=1024,
            num_gait_params=len(param_names),
            hidden_dims=[512, 256, 128]
        )
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        # Create a mock trainer object for scaler
        class MockTrainer:
            def __init__(self, scaler, device):
                self.scaler = scaler
                self.device = device
        
        trainer = MockTrainer(checkpoint['scaler'], config['device'])
        
        # Run comprehensive evaluation
        logger.info("Running comprehensive evaluation...")
        
        # Create test dataset and loader
        from torch.utils.data import DataLoader
        test_dataset = ComprehensiveGaitVideoDataset(
            test_paths, test_params,
            target_frames=config['target_frames'],
            target_size=config['target_size']
        )
        test_loader = DataLoader(test_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=4)
        
        # Evaluate model
        model.to(trainer.device)
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for videos, targets in tqdm(test_loader, desc="Testing"):
                try:
                    videos = videos.to(trainer.device)
                    targets = targets.to(trainer.device)
                    
                    predictions = model(videos)
                    
                    # Inverse transform predictions
                    pred_np = trainer.scaler.inverse_transform(predictions.cpu().numpy())
                    target_np = targets.cpu().numpy()
                    
                    all_predictions.append(pred_np)
                    all_targets.append(target_np)
                    
                except Exception as e:
                    logger.error(f"Error in test batch: {e}")
                    continue
        
        if not all_predictions:
            logger.error("No successful predictions made")
            return
        
        predictions = np.concatenate(all_predictions, axis=0)
        targets = np.concatenate(all_targets, axis=0)
        
        logger.info(f"Evaluation completed on {len(predictions)} samples")
        
        # Calculate per-parameter metrics
        metrics_df = []
        for i, param_name in enumerate(param_names):
            pred_param = predictions[:, i]
            true_param = targets[:, i]
            
            mae = mean_absolute_error(true_param, pred_param)
            rmse = np.sqrt(mean_squared_error(true_param, pred_param))
            r2 = r2_score(true_param, pred_param)
            
            metrics_df.append({
                'Parameter': param_name,
                'MAE': mae,
                'RMSE': rmse,
                'R²': r2
            })
        
        metrics_df = pd.DataFrame(metrics_df)
        
        # Save metrics
        metrics_df.to_csv("comprehensive_evaluation_metrics.csv", index=False)
        logger.info("Saved detailed metrics to comprehensive_evaluation_metrics.csv")
        
        # Print summary
        logger.info("=" * 80)
        logger.info("COMPREHENSIVE EVALUATION RESULTS")
        logger.info("=" * 80)
        
        # Sort by R² score for better readability
        metrics_sorted = metrics_df.sort_values('R²', ascending=False)
        
        logger.info("Per-parameter evaluation results (sorted by R² score):")
        logger.info("-" * 80)
        for _, row in metrics_sorted.iterrows():
            logger.info(f"{row['Parameter']:20s}: MAE={row['MAE']:8.4f}, RMSE={row['RMSE']:8.4f}, R²={row['R²']:7.4f}")
        
        # Overall metrics
        overall_mae = mean_absolute_error(targets, predictions)
        overall_rmse = np.sqrt(mean_squared_error(targets, predictions))
        overall_r2 = r2_score(targets, predictions)
        
        logger.info("-" * 80)
        logger.info(f"{'Overall Performance':20s}: MAE={overall_mae:8.4f}, RMSE={overall_rmse:8.4f}, R²={overall_r2:7.4f}")
        logger.info("=" * 80)
        
        # Create comprehensive visualizations
        logger.info("Creating comprehensive visualization plots...")
        
        # 1. Metrics summary bar plot
        plt.figure(figsize=(20, 12))
        
        # Top 10 parameters by R²
        top_params = metrics_df.nlargest(10, 'R²')
        
        plt.subplot(2, 3, 1)
        bars = plt.bar(range(len(top_params)), top_params['R²'], color='skyblue', alpha=0.8)
        plt.xlabel('Parameters')
        plt.ylabel('R² Score')
        plt.title('Top 10 Parameters by R² Score')
        plt.xticks(range(len(top_params)), top_params['Parameter'], rotation=45, ha='right')
        plt.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for i, bar in enumerate(bars):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{height:.3f}', ha='center', va='bottom', fontsize=8)
        
        plt.subplot(2, 3, 2)
        plt.bar(range(len(top_params)), top_params['MAE'], color='lightcoral', alpha=0.8)
        plt.xlabel('Parameters')
        plt.ylabel('MAE')
        plt.title('MAE for Top 10 Parameters')
        plt.xticks(range(len(top_params)), top_params['Parameter'], rotation=45, ha='right')
        plt.grid(True, alpha=0.3)
        
        plt.subplot(2, 3, 3)
        plt.bar(range(len(top_params)), top_params['RMSE'], color='lightgreen', alpha=0.8)
        plt.xlabel('Parameters')
        plt.ylabel('RMSE')
        plt.title('RMSE for Top 10 Parameters')
        plt.xticks(range(len(top_params)), top_params['Parameter'], rotation=45, ha='right')
        plt.grid(True, alpha=0.3)
        
        # 2. GT vs Pred scatter plots for key parameters
        key_params = ['Velocity', 'Cadence', 'Stride_Len_L', 'Stride_Len_R', 'Swing_Perc_L', 'Swing_Perc_R']
        key_indices = [param_names.index(param) for param in key_params if param in param_names]
        
        for i, param_idx in enumerate(key_indices[:3]):  # Show first 3 key parameters
            ax = plt.subplot(2, 3, 4 + i)
            param_name = param_names[param_idx]
            
            pred_param = predictions[:, param_idx]
            true_param = targets[:, param_idx]
            
            # Scatter plot
            ax.scatter(true_param, pred_param, alpha=0.6, s=30, color='blue')
            
            # Perfect prediction line
            min_val = min(true_param.min(), pred_param.min())
            max_val = max(true_param.max(), pred_param.max())
            ax.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2, label='Perfect Prediction')
            
            # Get metrics for this parameter
            param_metrics = metrics_df[metrics_df['Parameter'] == param_name].iloc[0]
            
            ax.set_xlabel(f'True {param_name}')
            ax.set_ylabel(f'Predicted {param_name}')
            ax.set_title(f'{param_name}\nR²={param_metrics["R²"]:.3f}, MAE={param_metrics["MAE"]:.3f}')
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig("comprehensive_evaluation_results.png", dpi=300, bbox_inches='tight')
        plt.close()
        
        # 3. Create additional scatter plots for all key parameters
        if len(key_indices) >= 6:
            fig, axes = plt.subplots(2, 3, figsize=(18, 12))
            axes = axes.flatten()
            
            for i, param_idx in enumerate(key_indices):
                ax = axes[i]
                param_name = param_names[param_idx]
                
                pred_param = predictions[:, param_idx]
                true_param = targets[:, param_idx]
                
                # Scatter plot
                ax.scatter(true_param, pred_param, alpha=0.6, s=20, color='blue')
                
                # Perfect prediction line
                min_val = min(true_param.min(), pred_param.min())
                max_val = max(true_param.max(), pred_param.max())
                ax.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2, label='Perfect Prediction')
                
                # Get metrics for this parameter
                param_metrics = metrics_df[metrics_df['Parameter'] == param_name].iloc[0]
                
                ax.set_xlabel(f'True {param_name}')
                ax.set_ylabel(f'Predicted {param_name}')
                ax.set_title(f'{param_name}\nR²={param_metrics["R²"]:.3f}, MAE={param_metrics["MAE"]:.3f}, RMSE={param_metrics["RMSE"]:.3f}')
                ax.legend()
                ax.grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig("key_parameters_scatter_plots.png", dpi=300, bbox_inches='tight')
            plt.close()
        
        logger.info("Comprehensive evaluation completed successfully!")
        logger.info("Generated files:")
        logger.info("- comprehensive_evaluation_metrics.csv")
        logger.info("- comprehensive_evaluation_results.png")
        logger.info("- key_parameters_scatter_plots.png")
        
        return metrics_df, predictions, targets
        
    except Exception as e:
        logger.error(f"Error in evaluation: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None


if __name__ == "__main__":
    main()
