#!/usr/bin/env python3
"""
Cosmos Video Tokenizer Based Gait Parameter Regression

This script implements a high-performance gait parameter regression model using
Cosmos Video Tokenizer's pre-trained video representations.

Two-stage approach:
1. Pre-encode all videos using Cosmos tokenizer to extract latent features
2. Train a lightweight regression model on the latent features
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import cv2
import pickle
import warnings
warnings.filterwarnings('ignore')

# Import Cosmos tokenizer
from cosmos_tokenizer.video_lib import CausalVideoTokenizer

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def load_and_preprocess_data(data_path="data/gaitrite_full_dataset.xlsx",
                           video_dir="data/videos/video_formated_trim"):
    """Load and preprocess the gait dataset."""
    logger.info("Loading gait dataset for Cosmos-based training...")
    
    # Load the dataset
    df = pd.read_excel(data_path)
    logger.info(f"Loaded dataset with {len(df)} samples and {len(df.columns)} columns")
    
    # Find Velocity column index and get all columns from Velocity onwards
    velocity_idx = df.columns.get_loc('Velocity')
    target_params = df.columns[velocity_idx:].tolist()
    
    logger.info(f"Target parameters ({len(target_params)}): {target_params}")
    
    # Extract video file names and check existence
    video_paths = []
    valid_indices = []
    
    for idx, row in df.iterrows():
        video_file = row['VideoFile']
        if pd.isna(video_file):
            continue
            
        filename = os.path.basename(video_file)
        video_path = os.path.join(video_dir, filename)
        
        if os.path.exists(video_path):
            video_paths.append(filename)
            valid_indices.append(idx)
    
    logger.info(f"Found {len(video_paths)} valid video files out of {len(df)} samples")
    
    # Extract gait parameters for valid samples
    valid_df = df.iloc[valid_indices]
    gait_params = valid_df[target_params].values.astype(np.float32)
    
    # Check for missing values
    missing_mask = np.isnan(gait_params).any(axis=1)
    if missing_mask.any():
        logger.warning(f"Removing {missing_mask.sum()} samples with missing gait parameters")
        video_paths = [video_paths[i] for i in range(len(video_paths)) if not missing_mask[i]]
        gait_params = gait_params[~missing_mask]
    
    logger.info(f"Final dataset: {len(video_paths)} samples with {len(target_params)} gait parameters")
    
    return video_paths, gait_params, target_params


class CosmosFeatureExtractor:
    """Cosmos Video Tokenizer based feature extractor."""
    
    def __init__(self, 
                 model_name="Cosmos-1.0-Tokenizer-CV8x8x8",
                 device="cuda",
                 dtype="bfloat16",
                 temporal_window=17):
        """
        Initialize Cosmos feature extractor.
        
        Args:
            model_name: Cosmos model name
            device: Device to run on
            dtype: Data type for computation
            temporal_window: Temporal window size
        """
        self.model_name = model_name
        self.device = device
        self.dtype = dtype
        self.temporal_window = temporal_window
        
        # Initialize Cosmos tokenizer
        encoder_ckpt = f"pretrained_ckpts/{model_name}/encoder.jit"
        
        if not os.path.exists(encoder_ckpt):
            raise FileNotFoundError(f"Encoder checkpoint not found: {encoder_ckpt}")
        
        logger.info(f"Loading Cosmos tokenizer: {model_name}")
        self.tokenizer = CausalVideoTokenizer(
            checkpoint_enc=encoder_ckpt,
            device=device,
            dtype=dtype,
        )
        
        logger.info(f"Successfully initialized {model_name} feature extractor")
    
    def load_and_preprocess_video(self, video_path, target_size=(224, 224)):
        """Load and preprocess a single video."""
        try:
            cap = cv2.VideoCapture(video_path)
            frames = []
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Resize and convert to RGB
                frame = cv2.resize(frame, target_size)
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                frames.append(frame)
            
            cap.release()
            
            if len(frames) == 0:
                logger.warning(f"No frames found in {video_path}")
                return None
            
            # Convert to numpy array
            video = np.array(frames)  # (T, H, W, 3)
            
            # Sample frames to temporal window size
            if len(video) >= self.temporal_window:
                indices = np.linspace(0, len(video) - 1, self.temporal_window, dtype=int)
                video = video[indices]
            else:
                # Pad with last frame if too short
                padding_needed = self.temporal_window - len(video)
                last_frame = video[-1:] if len(video) > 0 else np.zeros((1, *target_size, 3), dtype=np.uint8)
                padding = np.repeat(last_frame, padding_needed, axis=0)
                video = np.concatenate([video, padding], axis=0)
            
            return video
            
        except Exception as e:
            logger.error(f"Error loading video {video_path}: {e}")
            return None
    
    def extract_features(self, video):
        """Extract features from a video using Cosmos tokenizer."""
        if video is None:
            return None
        
        try:
            # Convert to tensor format expected by Cosmos
            # Input: (T, H, W, 3) -> (1, 3, T, H, W)
            video_tensor = torch.from_numpy(video).float()
            video_tensor = video_tensor.permute(3, 0, 1, 2).unsqueeze(0)  # (1, 3, T, H, W)
            
            # Normalize to [-1, 1] range
            video_tensor = (video_tensor / 255.0) * 2.0 - 1.0
            
            # Move to device and convert dtype
            video_tensor = video_tensor.to(self.device)
            if self.dtype == "bfloat16":
                video_tensor = video_tensor.to(torch.bfloat16)
            
            # Extract features using Cosmos encoder
            with torch.no_grad():
                latent_features = self.tokenizer.encode(video_tensor)
                if isinstance(latent_features, tuple):
                    latent_features = latent_features[0]
            
            # Convert back to float32 for downstream processing
            latent_features = latent_features.float().cpu()
            
            return latent_features
            
        except Exception as e:
            logger.error(f"Error extracting features: {e}")
            return None
    
    def extract_and_save_features(self, video_paths, video_dir, save_path, batch_size=1):
        """Extract features for all videos and save to disk."""
        logger.info(f"Extracting Cosmos features for {len(video_paths)} videos...")
        
        all_features = []
        valid_indices = []
        
        for i, video_filename in enumerate(tqdm(video_paths, desc="Extracting features")):
            video_path = os.path.join(video_dir, video_filename)
            
            # Load and preprocess video
            video = self.load_and_preprocess_video(video_path)
            
            # Extract features
            features = self.extract_features(video)
            
            if features is not None:
                # Flatten the features for easier handling
                # Shape: (1, 16, t, h, w) -> (16 * t * h * w,)
                features_flat = features.flatten().numpy()
                all_features.append(features_flat)
                valid_indices.append(i)
            else:
                logger.warning(f"Failed to extract features for {video_filename}")
        
        if all_features:
            # Convert to numpy array
            features_array = np.array(all_features)
            
            # Save features and valid indices
            save_data = {
                'features': features_array,
                'valid_indices': valid_indices,
                'feature_shape': features.shape if features is not None else None,
                'model_name': self.model_name
            }
            
            with open(save_path, 'wb') as f:
                pickle.dump(save_data, f)
            
            logger.info(f"Saved {len(all_features)} feature vectors to {save_path}")
            logger.info(f"Feature shape: {features_array.shape}")
            
            return features_array, valid_indices
        else:
            logger.error("No features were successfully extracted!")
            return None, None


class CosmosLatentDataset(Dataset):
    """Dataset for pre-extracted Cosmos latent features."""
    
    def __init__(self, features, gait_params):
        """
        Initialize dataset with pre-extracted features.
        
        Args:
            features: Pre-extracted Cosmos features (N, feature_dim)
            gait_params: Gait parameters (N, num_params)
        """
        self.features = features
        self.gait_params = gait_params
        
        logger.info(f"Initialized Cosmos latent dataset with {len(features)} samples")
        logger.info(f"Feature dimension: {features.shape[1]}")
        logger.info(f"Number of gait parameters: {gait_params.shape[1]}")
    
    def __len__(self):
        return len(self.features)
    
    def __getitem__(self, idx):
        features = torch.from_numpy(self.features[idx]).float()
        gait_params = torch.from_numpy(self.gait_params[idx]).float()
        return features, gait_params


class CosmosRegressionModel(nn.Module):
    """Lightweight regression model for Cosmos latent features."""
    
    def __init__(self, feature_dim, num_gait_params=28, hidden_dims=[1024, 512, 256]):
        """
        Initialize regression model.
        
        Args:
            feature_dim: Dimension of input Cosmos features
            num_gait_params: Number of gait parameters to predict
            hidden_dims: Hidden layer dimensions
        """
        super().__init__()
        
        # Build regression network
        layers = []
        prev_dim = feature_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.BatchNorm1d(hidden_dim)
            ])
            prev_dim = hidden_dim
        
        # Output layer
        layers.append(nn.Linear(prev_dim, num_gait_params))
        
        self.regressor = nn.Sequential(*layers)
        
        total_params = sum(p.numel() for p in self.parameters())
        logger.info(f"Initialized Cosmos regression model with {total_params:,} parameters")
    
    def forward(self, x):
        return self.regressor(x)


def main():
    """Main function for Cosmos-based gait regression."""
    logger.info("Starting Cosmos-based Gait Parameter Regression")
    
    config = {
        'device': 'cuda' if torch.cuda.is_available() else 'cpu',
        'model_name': 'Cosmos-1.0-Tokenizer-CV8x8x8',
        'dtype': 'bfloat16',
        'temporal_window': 17,
        'batch_size': 16,
        'epochs': 50,
        'learning_rate': 1e-3,
        'weight_decay': 1e-4,
        'subset_size': 1000,  # Use more data with efficient Cosmos features
        'test_size': 0.2,
        'val_size': 0.2,
        'features_cache_path': 'cosmos_features_cache.pkl'
    }
    
    logger.info(f"Configuration: {config}")
    
    try:
        # Load data
        video_paths, gait_params, param_names = load_and_preprocess_data()
        
        # Use subset for initial testing
        subset_size = min(config['subset_size'], len(video_paths))
        video_paths = video_paths[:subset_size]
        gait_params = gait_params[:subset_size]
        
        logger.info(f"Using subset of {subset_size} samples")
        
        # Check if features are already cached
        if os.path.exists(config['features_cache_path']):
            logger.info("Loading cached Cosmos features...")
            with open(config['features_cache_path'], 'rb') as f:
                cache_data = pickle.load(f)
            
            features = cache_data['features']
            valid_indices = cache_data['valid_indices']
            
            # Filter gait params to match valid indices
            gait_params = gait_params[valid_indices]
            
            logger.info(f"Loaded {len(features)} cached features")
        else:
            # Extract features using Cosmos
            logger.info("Extracting features using Cosmos tokenizer...")
            feature_extractor = CosmosFeatureExtractor(
                model_name=config['model_name'],
                device=config['device'],
                dtype=config['dtype'],
                temporal_window=config['temporal_window']
            )
            
            features, valid_indices = feature_extractor.extract_and_save_features(
                video_paths=video_paths,
                video_dir="data/videos/video_formated_trim",
                save_path=config['features_cache_path']
            )
            
            if features is None:
                logger.error("Feature extraction failed!")
                return None
            
            # Filter gait params to match valid indices
            gait_params = gait_params[valid_indices]
        
        logger.info(f"Final dataset: {len(features)} samples with {len(param_names)} parameters")
        
        return features, gait_params, param_names, config
        
    except Exception as e:
        logger.error(f"Error in main: {e}")
        import traceback
        traceback.print_exc()
        return None


class CosmosTrainer:
    """Trainer for Cosmos-based regression model."""

    def __init__(self, model, device="cuda", learning_rate=1e-3, weight_decay=1e-4):
        self.device = device
        self.model = model.to(device)
        self.optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(self.optimizer, patience=5, factor=0.5)
        self.criterion = nn.MSELoss()

        # Standardization
        self.scaler = StandardScaler()
        self.target_fitted = False

        self.train_losses = []
        self.val_losses = []
        self.best_val_loss = float('inf')

        logger.info(f"Initialized Cosmos trainer on {device}")

    def fit_scaler(self, targets):
        """Fit the scaler on training targets."""
        if not self.target_fitted:
            self.scaler.fit(targets)
            self.target_fitted = True
            logger.info("Fitted target scaler")

    def train_epoch(self, dataloader):
        """Train for one epoch."""
        self.model.train()
        total_loss = 0.0
        num_batches = 0

        for features, targets in tqdm(dataloader, desc="Training"):
            try:
                features = features.to(self.device)
                targets = targets.to(self.device)

                # Standardize targets
                targets_np = targets.cpu().numpy()
                targets_scaled = self.scaler.transform(targets_np)
                targets = torch.from_numpy(targets_scaled).float().to(self.device)

                self.optimizer.zero_grad()
                predictions = self.model(features)
                loss = self.criterion(predictions, targets)

                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                self.optimizer.step()

                total_loss += loss.item()
                num_batches += 1

            except Exception as e:
                logger.error(f"Error in training batch: {e}")
                continue

        if num_batches > 0:
            avg_loss = total_loss / num_batches
            self.train_losses.append(avg_loss)
            return avg_loss
        else:
            return float('inf')

    def validate(self, dataloader):
        """Validate the model."""
        self.model.eval()
        total_loss = 0.0
        all_predictions = []
        all_targets = []
        num_batches = 0

        with torch.no_grad():
            for features, targets in tqdm(dataloader, desc="Validating"):
                try:
                    features = features.to(self.device)
                    targets = targets.to(self.device)

                    # Standardize targets
                    targets_np = targets.cpu().numpy()
                    targets_scaled = self.scaler.transform(targets_np)
                    targets_scaled_tensor = torch.from_numpy(targets_scaled).float().to(self.device)

                    predictions = self.model(features)
                    loss = self.criterion(predictions, targets_scaled_tensor)

                    total_loss += loss.item()
                    num_batches += 1

                    # Inverse transform for metrics calculation
                    pred_np = self.scaler.inverse_transform(predictions.cpu().numpy())
                    all_predictions.append(pred_np)
                    all_targets.append(targets_np)

                except Exception as e:
                    logger.error(f"Error in validation batch: {e}")
                    continue

        if num_batches > 0:
            avg_loss = total_loss / num_batches
            self.val_losses.append(avg_loss)

            if all_predictions and all_targets:
                predictions = np.concatenate(all_predictions, axis=0)
                targets = np.concatenate(all_targets, axis=0)

                r2 = r2_score(targets, predictions)
                mae = mean_absolute_error(targets, predictions)
                rmse = np.sqrt(mean_squared_error(targets, predictions))

                return avg_loss, {'r2': r2, 'mae': mae, 'rmse': rmse}
            else:
                return avg_loss, {'r2': 0.0, 'mae': float('inf'), 'rmse': float('inf')}
        else:
            return float('inf'), {'r2': 0.0, 'mae': float('inf'), 'rmse': float('inf')}

    def fit(self, train_loader, val_loader, epochs=50, save_path="cosmos_best_model.pth"):
        """Train the Cosmos-based model."""
        logger.info(f"Starting Cosmos-based training for {epochs} epochs...")

        # Fit scaler on training data
        all_targets = []
        for _, targets in train_loader:
            all_targets.append(targets.numpy())
        all_targets = np.concatenate(all_targets, axis=0)
        self.fit_scaler(all_targets)

        for epoch in range(epochs):
            train_loss = self.train_epoch(train_loader)
            val_loss, metrics = self.validate(val_loader)

            # Update learning rate
            self.scheduler.step(val_loss)
            current_lr = self.optimizer.param_groups[0]['lr']

            logger.info(f"Epoch {epoch+1}/{epochs}: "
                       f"Train Loss: {train_loss:.4f}, "
                       f"Val Loss: {val_loss:.4f}, "
                       f"Val R²: {metrics['r2']:.4f}, "
                       f"Val MAE: {metrics['mae']:.4f}, "
                       f"Val RMSE: {metrics['rmse']:.4f}, "
                       f"LR: {current_lr:.6f}")

            if val_loss < self.best_val_loss:
                self.best_val_loss = val_loss
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'scaler': self.scaler,
                    'val_loss': val_loss,
                    'metrics': metrics
                }, save_path)
                logger.info(f"Saved best model with validation loss: {val_loss:.4f}")

        return {
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'best_val_loss': self.best_val_loss
        }


def train_cosmos_model(features, gait_params, param_names, config):
    """Train the Cosmos-based regression model."""
    logger.info("Starting Cosmos-based model training...")

    # Create data splits
    train_val_features, test_features, train_val_params, test_params = train_test_split(
        features, gait_params, test_size=config['test_size'], random_state=42
    )

    train_features, val_features, train_params, val_params = train_test_split(
        train_val_features, train_val_params, test_size=config['val_size'], random_state=42
    )

    logger.info(f"Data split: Train={len(train_features)}, Val={len(val_features)}, Test={len(test_features)}")

    # Create datasets and loaders
    train_dataset = CosmosLatentDataset(train_features, train_params)
    val_dataset = CosmosLatentDataset(val_features, val_params)
    test_dataset = CosmosLatentDataset(test_features, test_params)

    train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True, num_workers=4)
    val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=4)
    test_loader = DataLoader(test_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=4)

    # Initialize model
    feature_dim = features.shape[1]
    model = CosmosRegressionModel(
        feature_dim=feature_dim,
        num_gait_params=len(param_names),
        hidden_dims=[1024, 512, 256]
    )

    # Initialize trainer
    trainer = CosmosTrainer(
        model=model,
        device=config['device'],
        learning_rate=config['learning_rate'],
        weight_decay=config['weight_decay']
    )

    # Train the model
    history = trainer.fit(
        train_loader=train_loader,
        val_loader=val_loader,
        epochs=config['epochs'],
        save_path="cosmos_best_model.pth"
    )

    logger.info("Cosmos-based training completed successfully!")
    return history, test_loader, param_names, trainer


def evaluate_cosmos_model(model, trainer, test_loader, param_names):
    """Evaluate the Cosmos-based model."""
    logger.info("Starting Cosmos model evaluation...")

    model.eval()
    all_predictions = []
    all_targets = []

    with torch.no_grad():
        for features, targets in tqdm(test_loader, desc="Testing"):
            try:
                features = features.to(trainer.device)
                targets = targets.to(trainer.device)

                predictions = model(features)

                # Inverse transform predictions
                pred_np = trainer.scaler.inverse_transform(predictions.cpu().numpy())
                target_np = targets.cpu().numpy()

                all_predictions.append(pred_np)
                all_targets.append(target_np)

            except Exception as e:
                logger.error(f"Error in test batch: {e}")
                continue

    if not all_predictions:
        logger.error("No successful predictions made")
        return None

    predictions = np.concatenate(all_predictions, axis=0)
    targets = np.concatenate(all_targets, axis=0)

    # Calculate per-parameter metrics
    metrics_df = []
    for i, param_name in enumerate(param_names):
        pred_param = predictions[:, i]
        true_param = targets[:, i]

        mae = mean_absolute_error(true_param, pred_param)
        rmse = np.sqrt(mean_squared_error(true_param, pred_param))
        r2 = r2_score(true_param, pred_param)

        metrics_df.append({
            'Parameter': param_name,
            'MAE': mae,
            'RMSE': rmse,
            'R²': r2
        })

    metrics_df = pd.DataFrame(metrics_df)

    # Save metrics
    metrics_df.to_csv("cosmos_evaluation_metrics.csv", index=False)
    logger.info("Saved detailed metrics to cosmos_evaluation_metrics.csv")

    # Print summary
    logger.info("=" * 80)
    logger.info("COSMOS-BASED EVALUATION RESULTS")
    logger.info("=" * 80)

    # Sort by R² score for better readability
    metrics_sorted = metrics_df.sort_values('R²', ascending=False)

    logger.info("Per-parameter evaluation results (sorted by R² score):")
    logger.info("-" * 80)
    for _, row in metrics_sorted.iterrows():
        logger.info(f"{row['Parameter']:20s}: MAE={row['MAE']:8.4f}, RMSE={row['RMSE']:8.4f}, R²={row['R²']:7.4f}")

    # Overall metrics
    overall_mae = mean_absolute_error(targets, predictions)
    overall_rmse = np.sqrt(mean_squared_error(targets, predictions))
    overall_r2 = r2_score(targets, predictions)

    logger.info("-" * 80)
    logger.info(f"{'Overall Performance':20s}: MAE={overall_mae:8.4f}, RMSE={overall_rmse:8.4f}, R²={overall_r2:7.4f}")
    logger.info("=" * 80)

    return metrics_df, predictions, targets


if __name__ == "__main__":
    # Extract features
    result = main()
    if result is not None:
        features, gait_params, param_names, config = result
        logger.info("Cosmos feature extraction completed successfully!")

        # Train model
        history, test_loader, param_names, trainer = train_cosmos_model(
            features, gait_params, param_names, config
        )

        # Load best model for evaluation
        checkpoint = torch.load("cosmos_best_model.pth", weights_only=False)
        model = CosmosRegressionModel(
            feature_dim=features.shape[1],
            num_gait_params=len(param_names),
            hidden_dims=[1024, 512, 256]
        )
        model.load_state_dict(checkpoint['model_state_dict'])
        model.to(config['device'])  # Move model to correct device
        trainer.scaler = checkpoint['scaler']

        # Evaluate model
        metrics_df, predictions, targets = evaluate_cosmos_model(
            model, trainer, test_loader, param_names
        )

        logger.info("Cosmos-based gait regression completed successfully!")
    else:
        logger.error("Cosmos feature extraction failed!")
