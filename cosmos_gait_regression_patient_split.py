#!/usr/bin/env python3
"""
Patient-aware Cosmos Video Tokenizer Based Gait Parameter Regression

This script implements a high-performance gait parameter regression model using
Cosmos Video Tokenizer's pre-trained video representations with proper patient-based
data splitting to prevent data leakage.

Key improvements:
- Patient ID-based data splitting (no patient appears in multiple splits)
- Prevents data leakage for reliable evaluation
- Uses Cosmos tokenizer for superior feature extraction
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import cv2
import pickle
import warnings
warnings.filterwarnings('ignore')

# Import Cosmos tokenizer
from cosmos_tokenizer.video_lib import CausalVideoTokenizer

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def extract_patient_id_from_filename(filename):
    """Extract patient ID from video filename."""
    try:
        # Extract basename
        basename = os.path.basename(filename)
        # Extract patient ID (before first underscore)
        patient_id = basename.split('_')[0]
        return patient_id
    except:
        return None


def patient_aware_data_split(df, test_size=0.2, val_size=0.2, random_state=42):
    """
    Split data by patient ID to prevent data leakage.
    
    Args:
        df: DataFrame with patient information
        test_size: Fraction for test set
        val_size: Fraction of remaining data for validation
        random_state: Random seed
    
    Returns:
        train_df, val_df, test_df
    """
    logger.info("Performing patient-aware data splitting...")
    
    # Use Pt_id column if available, otherwise extract from VideoFile
    if 'Pt_id' in df.columns:
        patient_ids = df['Pt_id'].unique()
        logger.info(f"Using Pt_id column: {len(patient_ids)} unique patients")
    else:
        # Extract patient IDs from video filenames
        df['extracted_patient_id'] = df['VideoFile'].apply(extract_patient_id_from_filename)
        patient_ids = df['extracted_patient_id'].unique()
        logger.info(f"Extracted patient IDs from filenames: {len(patient_ids)} unique patients")
    
    # Remove any None values
    patient_ids = [pid for pid in patient_ids if pid is not None]
    logger.info(f"Valid patient IDs: {len(patient_ids)}")
    
    # Split patients (not samples) into train/val/test
    train_val_patients, test_patients = train_test_split(
        patient_ids, test_size=test_size, random_state=random_state
    )
    
    train_patients, val_patients = train_test_split(
        train_val_patients, test_size=val_size, random_state=random_state
    )
    
    logger.info(f"Patient split: Train={len(train_patients)}, Val={len(val_patients)}, Test={len(test_patients)}")
    
    # Create data splits based on patient assignment
    if 'Pt_id' in df.columns:
        train_df = df[df['Pt_id'].isin(train_patients)].copy()
        val_df = df[df['Pt_id'].isin(val_patients)].copy()
        test_df = df[df['Pt_id'].isin(test_patients)].copy()
    else:
        train_df = df[df['extracted_patient_id'].isin(train_patients)].copy()
        val_df = df[df['extracted_patient_id'].isin(val_patients)].copy()
        test_df = df[df['extracted_patient_id'].isin(test_patients)].copy()
    
    logger.info(f"Sample split: Train={len(train_df)}, Val={len(val_df)}, Test={len(test_df)}")
    
    # Verify no patient overlap
    if 'Pt_id' in df.columns:
        train_pts = set(train_df['Pt_id'].unique())
        val_pts = set(val_df['Pt_id'].unique())
        test_pts = set(test_df['Pt_id'].unique())
    else:
        train_pts = set(train_df['extracted_patient_id'].unique())
        val_pts = set(val_df['extracted_patient_id'].unique())
        test_pts = set(test_df['extracted_patient_id'].unique())
    
    # Check for overlaps
    train_val_overlap = train_pts.intersection(val_pts)
    train_test_overlap = train_pts.intersection(test_pts)
    val_test_overlap = val_pts.intersection(test_pts)
    
    if train_val_overlap or train_test_overlap or val_test_overlap:
        logger.error(f"Patient overlap detected!")
        logger.error(f"Train-Val overlap: {train_val_overlap}")
        logger.error(f"Train-Test overlap: {train_test_overlap}")
        logger.error(f"Val-Test overlap: {val_test_overlap}")
        raise ValueError("Patient overlap detected in data splits!")
    else:
        logger.info("✅ No patient overlap detected - data splits are clean!")
    
    return train_df, val_df, test_df


def load_and_preprocess_data_patient_aware(data_path="data/gaitrite_full_dataset.xlsx",
                                         video_dir="data/videos/video_formated_trim",
                                         test_size=0.2, val_size=0.2, random_state=42):
    """Load and preprocess the gait dataset with patient-aware splitting."""
    logger.info("Loading gait dataset with patient-aware splitting...")
    
    # Load the dataset
    df = pd.read_excel(data_path)
    logger.info(f"Loaded dataset with {len(df)} samples and {len(df.columns)} columns")
    
    # Find Velocity column index and get all columns from Velocity onwards
    velocity_idx = df.columns.get_loc('Velocity')
    target_params = df.columns[velocity_idx:].tolist()
    
    logger.info(f"Target parameters ({len(target_params)}): {target_params}")
    
    # Filter valid video files first
    valid_rows = []
    for idx, row in df.iterrows():
        video_file = row['VideoFile']
        if pd.isna(video_file):
            continue
            
        filename = os.path.basename(video_file)
        video_path = os.path.join(video_dir, filename)
        
        if os.path.exists(video_path):
            valid_rows.append(idx)
    
    # Keep only valid rows
    df_valid = df.iloc[valid_rows].copy()
    logger.info(f"Found {len(df_valid)} samples with valid video files")
    
    # Remove samples with missing gait parameters
    gait_data = df_valid[target_params].values
    missing_mask = np.isnan(gait_data).any(axis=1)
    if missing_mask.any():
        logger.warning(f"Removing {missing_mask.sum()} samples with missing gait parameters")
        df_valid = df_valid[~missing_mask].copy()
    
    logger.info(f"Final valid dataset: {len(df_valid)} samples")
    
    # Perform patient-aware splitting
    train_df, val_df, test_df = patient_aware_data_split(
        df_valid, test_size=test_size, val_size=val_size, random_state=random_state
    )
    
    # Extract data for each split
    def extract_split_data(split_df):
        video_paths = []
        gait_params = []
        
        for _, row in split_df.iterrows():
            filename = os.path.basename(row['VideoFile'])
            video_paths.append(filename)
            gait_params.append(row[target_params].values)
        
        return video_paths, np.array(gait_params, dtype=np.float32)
    
    train_paths, train_params = extract_split_data(train_df)
    val_paths, val_params = extract_split_data(val_df)
    test_paths, test_params = extract_split_data(test_df)
    
    logger.info(f"Final splits - Train: {len(train_paths)}, Val: {len(val_paths)}, Test: {len(test_paths)}")
    
    return (train_paths, train_params), (val_paths, val_params), (test_paths, test_params), target_params


class CosmosFeatureExtractor:
    """Cosmos Video Tokenizer based feature extractor."""
    
    def __init__(self, 
                 model_name="Cosmos-1.0-Tokenizer-CV8x8x8",
                 device="cuda",
                 dtype="bfloat16",
                 temporal_window=17):
        """Initialize Cosmos feature extractor."""
        self.model_name = model_name
        self.device = device
        self.dtype = dtype
        self.temporal_window = temporal_window
        
        # Initialize Cosmos tokenizer
        encoder_ckpt = f"pretrained_ckpts/{model_name}/encoder.jit"
        
        if not os.path.exists(encoder_ckpt):
            raise FileNotFoundError(f"Encoder checkpoint not found: {encoder_ckpt}")
        
        logger.info(f"Loading Cosmos tokenizer: {model_name}")
        self.tokenizer = CausalVideoTokenizer(
            checkpoint_enc=encoder_ckpt,
            device=device,
            dtype=dtype,
        )
        
        logger.info(f"Successfully initialized {model_name} feature extractor")
    
    def load_and_preprocess_video(self, video_path, target_size=(224, 224)):
        """Load and preprocess a single video."""
        try:
            cap = cv2.VideoCapture(video_path)
            frames = []
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Resize and convert to RGB
                frame = cv2.resize(frame, target_size)
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                frames.append(frame)
            
            cap.release()
            
            if len(frames) == 0:
                logger.warning(f"No frames found in {video_path}")
                return None
            
            # Convert to numpy array
            video = np.array(frames)  # (T, H, W, 3)
            
            # Sample frames to temporal window size
            if len(video) >= self.temporal_window:
                indices = np.linspace(0, len(video) - 1, self.temporal_window, dtype=int)
                video = video[indices]
            else:
                # Pad with last frame if too short
                padding_needed = self.temporal_window - len(video)
                last_frame = video[-1:] if len(video) > 0 else np.zeros((1, *target_size, 3), dtype=np.uint8)
                padding = np.repeat(last_frame, padding_needed, axis=0)
                video = np.concatenate([video, padding], axis=0)
            
            return video
            
        except Exception as e:
            logger.error(f"Error loading video {video_path}: {e}")
            return None
    
    def extract_features(self, video):
        """Extract features from a video using Cosmos tokenizer."""
        if video is None:
            return None
        
        try:
            # Convert to tensor format expected by Cosmos
            # Input: (T, H, W, 3) -> (1, 3, T, H, W)
            video_tensor = torch.from_numpy(video).float()
            video_tensor = video_tensor.permute(3, 0, 1, 2).unsqueeze(0)  # (1, 3, T, H, W)
            
            # Normalize to [-1, 1] range
            video_tensor = (video_tensor / 255.0) * 2.0 - 1.0
            
            # Move to device and convert dtype
            video_tensor = video_tensor.to(self.device)
            if self.dtype == "bfloat16":
                video_tensor = video_tensor.to(torch.bfloat16)
            
            # Extract features using Cosmos encoder
            with torch.no_grad():
                latent_features = self.tokenizer.encode(video_tensor)
                if isinstance(latent_features, tuple):
                    latent_features = latent_features[0]
            
            # Convert back to float32 for downstream processing
            latent_features = latent_features.float().cpu()
            
            return latent_features
            
        except Exception as e:
            logger.error(f"Error extracting features: {e}")
            return None
    
    def extract_and_save_features(self, video_paths, video_dir, save_path):
        """Extract features for all videos and save to disk."""
        logger.info(f"Extracting Cosmos features for {len(video_paths)} videos...")
        
        all_features = []
        valid_indices = []
        
        for i, video_filename in enumerate(tqdm(video_paths, desc="Extracting features")):
            video_path = os.path.join(video_dir, video_filename)
            
            # Load and preprocess video
            video = self.load_and_preprocess_video(video_path)
            
            # Extract features
            features = self.extract_features(video)
            
            if features is not None:
                # Flatten the features for easier handling
                features_flat = features.flatten().numpy()
                all_features.append(features_flat)
                valid_indices.append(i)
            else:
                logger.warning(f"Failed to extract features for {video_filename}")
        
        if all_features:
            # Convert to numpy array
            features_array = np.array(all_features)
            
            # Save features and valid indices
            save_data = {
                'features': features_array,
                'valid_indices': valid_indices,
                'feature_shape': features.shape if features is not None else None,
                'model_name': self.model_name
            }
            
            with open(save_path, 'wb') as f:
                pickle.dump(save_data, f)
            
            logger.info(f"Saved {len(all_features)} feature vectors to {save_path}")
            logger.info(f"Feature shape: {features_array.shape}")
            
            return features_array, valid_indices
        else:
            logger.error("No features were successfully extracted!")
            return None, None


class CosmosLatentDataset(Dataset):
    """Dataset for pre-extracted Cosmos latent features."""

    def __init__(self, features, gait_params):
        """Initialize dataset with pre-extracted features."""
        self.features = features
        self.gait_params = gait_params

        logger.info(f"Initialized Cosmos latent dataset with {len(features)} samples")
        logger.info(f"Feature dimension: {features.shape[1]}")
        logger.info(f"Number of gait parameters: {gait_params.shape[1]}")

    def __len__(self):
        return len(self.features)

    def __getitem__(self, idx):
        features = torch.from_numpy(self.features[idx]).float()
        gait_params = torch.from_numpy(self.gait_params[idx]).float()
        return features, gait_params


class CosmosRegressionModel(nn.Module):
    """Lightweight regression model for Cosmos latent features."""

    def __init__(self, feature_dim, num_gait_params=28, hidden_dims=[1024, 512, 256]):
        """Initialize regression model."""
        super().__init__()

        # Build regression network
        layers = []
        prev_dim = feature_dim

        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.BatchNorm1d(hidden_dim)
            ])
            prev_dim = hidden_dim

        # Output layer
        layers.append(nn.Linear(prev_dim, num_gait_params))

        self.regressor = nn.Sequential(*layers)

        total_params = sum(p.numel() for p in self.parameters())
        logger.info(f"Initialized Cosmos regression model with {total_params:,} parameters")

    def forward(self, x):
        return self.regressor(x)


class CosmosTrainer:
    """Trainer for Cosmos-based regression model."""

    def __init__(self, model, device="cuda", learning_rate=1e-3, weight_decay=1e-4):
        self.device = device
        self.model = model.to(device)
        self.optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(self.optimizer, patience=5, factor=0.5)
        self.criterion = nn.MSELoss()

        # Standardization
        self.scaler = StandardScaler()
        self.target_fitted = False

        self.train_losses = []
        self.val_losses = []
        self.best_val_loss = float('inf')

        logger.info(f"Initialized Cosmos trainer on {device}")

    def fit_scaler(self, targets):
        """Fit the scaler on training targets."""
        if not self.target_fitted:
            self.scaler.fit(targets)
            self.target_fitted = True
            logger.info("Fitted target scaler")

    def train_epoch(self, dataloader):
        """Train for one epoch."""
        self.model.train()
        total_loss = 0.0
        num_batches = 0

        for features, targets in tqdm(dataloader, desc="Training"):
            try:
                features = features.to(self.device)
                targets = targets.to(self.device)

                # Standardize targets
                targets_np = targets.cpu().numpy()
                targets_scaled = self.scaler.transform(targets_np)
                targets = torch.from_numpy(targets_scaled).float().to(self.device)

                self.optimizer.zero_grad()
                predictions = self.model(features)
                loss = self.criterion(predictions, targets)

                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                self.optimizer.step()

                total_loss += loss.item()
                num_batches += 1

            except Exception as e:
                logger.error(f"Error in training batch: {e}")
                continue

        if num_batches > 0:
            avg_loss = total_loss / num_batches
            self.train_losses.append(avg_loss)
            return avg_loss
        else:
            return float('inf')

    def validate(self, dataloader):
        """Validate the model."""
        self.model.eval()
        total_loss = 0.0
        all_predictions = []
        all_targets = []
        num_batches = 0

        with torch.no_grad():
            for features, targets in tqdm(dataloader, desc="Validating"):
                try:
                    features = features.to(self.device)
                    targets = targets.to(self.device)

                    # Standardize targets
                    targets_np = targets.cpu().numpy()
                    targets_scaled = self.scaler.transform(targets_np)
                    targets_scaled_tensor = torch.from_numpy(targets_scaled).float().to(self.device)

                    predictions = self.model(features)
                    loss = self.criterion(predictions, targets_scaled_tensor)

                    total_loss += loss.item()
                    num_batches += 1

                    # Inverse transform for metrics calculation
                    pred_np = self.scaler.inverse_transform(predictions.cpu().numpy())
                    all_predictions.append(pred_np)
                    all_targets.append(targets_np)

                except Exception as e:
                    logger.error(f"Error in validation batch: {e}")
                    continue

        if num_batches > 0:
            avg_loss = total_loss / num_batches
            self.val_losses.append(avg_loss)

            if all_predictions and all_targets:
                predictions = np.concatenate(all_predictions, axis=0)
                targets = np.concatenate(all_targets, axis=0)

                r2 = r2_score(targets, predictions)
                mae = mean_absolute_error(targets, predictions)
                rmse = np.sqrt(mean_squared_error(targets, predictions))

                return avg_loss, {'r2': r2, 'mae': mae, 'rmse': rmse}
            else:
                return avg_loss, {'r2': 0.0, 'mae': float('inf'), 'rmse': float('inf')}
        else:
            return float('inf'), {'r2': 0.0, 'mae': float('inf'), 'rmse': float('inf')}

    def fit(self, train_loader, val_loader, epochs=50, save_path="cosmos_patient_split_best_model.pth"):
        """Train the Cosmos-based model."""
        logger.info(f"Starting patient-aware Cosmos training for {epochs} epochs...")

        # Fit scaler on training data
        all_targets = []
        for _, targets in train_loader:
            all_targets.append(targets.numpy())
        all_targets = np.concatenate(all_targets, axis=0)
        self.fit_scaler(all_targets)

        for epoch in range(epochs):
            train_loss = self.train_epoch(train_loader)
            val_loss, metrics = self.validate(val_loader)

            # Update learning rate
            self.scheduler.step(val_loss)
            current_lr = self.optimizer.param_groups[0]['lr']

            logger.info(f"Epoch {epoch+1}/{epochs}: "
                       f"Train Loss: {train_loss:.4f}, "
                       f"Val Loss: {val_loss:.4f}, "
                       f"Val R²: {metrics['r2']:.4f}, "
                       f"Val MAE: {metrics['mae']:.4f}, "
                       f"Val RMSE: {metrics['rmse']:.4f}, "
                       f"LR: {current_lr:.6f}")

            if val_loss < self.best_val_loss:
                self.best_val_loss = val_loss
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'scaler': self.scaler,
                    'val_loss': val_loss,
                    'metrics': metrics
                }, save_path)
                logger.info(f"Saved best model with validation loss: {val_loss:.4f}")

        return {
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'best_val_loss': self.best_val_loss
        }


def main():
    """Main function for patient-aware Cosmos-based gait regression."""
    logger.info("Starting Patient-Aware Cosmos-based Gait Parameter Regression")

    config = {
        'device': 'cuda' if torch.cuda.is_available() else 'cpu',
        'model_name': 'Cosmos-1.0-Tokenizer-CV8x8x8',
        'dtype': 'bfloat16',
        'temporal_window': 17,
        'batch_size': 16,
        'epochs': 50,
        'learning_rate': 1e-3,
        'weight_decay': 1e-4,
        'subset_size': 1000,  # Use subset for initial testing
        'test_size': 0.2,
        'val_size': 0.2,
        'features_cache_path': 'cosmos_patient_split_features_cache.pkl'
    }

    logger.info(f"Configuration: {config}")

    try:
        # Load data with patient-aware splitting
        (train_paths, train_params), (val_paths, val_params), (test_paths, test_params), param_names = \
            load_and_preprocess_data_patient_aware(
                test_size=config['test_size'],
                val_size=config['val_size'],
                random_state=42
            )

        # Use subset for initial testing
        subset_size = min(config['subset_size'], len(train_paths) + len(val_paths) + len(test_paths))

        # Proportionally reduce each split
        train_subset = int(len(train_paths) * subset_size / (len(train_paths) + len(val_paths) + len(test_paths)))
        val_subset = int(len(val_paths) * subset_size / (len(train_paths) + len(val_paths) + len(test_paths)))
        test_subset = subset_size - train_subset - val_subset

        train_paths = train_paths[:train_subset]
        train_params = train_params[:train_subset]
        val_paths = val_paths[:val_subset]
        val_params = val_params[:val_subset]
        test_paths = test_paths[:test_subset]
        test_params = test_params[:test_subset]

        logger.info(f"Using subset - Train: {len(train_paths)}, Val: {len(val_paths)}, Test: {len(test_paths)}")

        # Check if features are already cached
        if os.path.exists(config['features_cache_path']):
            logger.info("Loading cached Cosmos features...")
            with open(config['features_cache_path'], 'rb') as f:
                cache_data = pickle.load(f)

            # Extract features for each split
            all_paths = train_paths + val_paths + test_paths
            train_features = cache_data['features'][:len(train_paths)]
            val_features = cache_data['features'][len(train_paths):len(train_paths)+len(val_paths)]
            test_features = cache_data['features'][len(train_paths)+len(val_paths):]

            logger.info(f"Loaded cached features - Train: {train_features.shape}, Val: {val_features.shape}, Test: {test_features.shape}")
        else:
            # Extract features using Cosmos
            logger.info("Extracting features using Cosmos tokenizer...")
            feature_extractor = CosmosFeatureExtractor(
                model_name=config['model_name'],
                device=config['device'],
                dtype=config['dtype'],
                temporal_window=config['temporal_window']
            )

            # Extract features for all splits
            all_paths = train_paths + val_paths + test_paths
            all_features, valid_indices = feature_extractor.extract_and_save_features(
                video_paths=all_paths,
                video_dir="data/videos/video_formated_trim",
                save_path=config['features_cache_path']
            )

            if all_features is None:
                logger.error("Feature extraction failed!")
                return None

            # Split features back into train/val/test
            train_features = all_features[:len(train_paths)]
            val_features = all_features[len(train_paths):len(train_paths)+len(val_paths)]
            test_features = all_features[len(train_paths)+len(val_paths):]

        logger.info(f"Final dataset - Train: {len(train_features)}, Val: {len(val_features)}, Test: {len(test_features)}")

        # Create datasets and loaders
        train_dataset = CosmosLatentDataset(train_features, train_params)
        val_dataset = CosmosLatentDataset(val_features, val_params)
        test_dataset = CosmosLatentDataset(test_features, test_params)

        train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True, num_workers=4)
        val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=4)
        test_loader = DataLoader(test_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=4)

        # Initialize model
        feature_dim = train_features.shape[1]
        model = CosmosRegressionModel(
            feature_dim=feature_dim,
            num_gait_params=len(param_names),
            hidden_dims=[1024, 512, 256]
        )

        # Initialize trainer
        trainer = CosmosTrainer(
            model=model,
            device=config['device'],
            learning_rate=config['learning_rate'],
            weight_decay=config['weight_decay']
        )

        # Train the model
        history = trainer.fit(
            train_loader=train_loader,
            val_loader=val_loader,
            epochs=config['epochs'],
            save_path="cosmos_patient_split_best_model.pth"
        )

        logger.info("Patient-aware Cosmos training completed successfully!")

        return {
            'model': model,
            'trainer': trainer,
            'test_loader': test_loader,
            'param_names': param_names,
            'history': history,
            'config': config
        }

    except Exception as e:
        logger.error(f"Error in main: {e}")
        import traceback
        traceback.print_exc()
        return None


def evaluate_patient_split_model(model, trainer, test_loader, param_names):
    """Evaluate the patient-split Cosmos-based model."""
    logger.info("Starting patient-aware Cosmos model evaluation...")

    model.eval()
    all_predictions = []
    all_targets = []

    with torch.no_grad():
        for features, targets in tqdm(test_loader, desc="Testing"):
            try:
                features = features.to(trainer.device)
                targets = targets.to(trainer.device)

                predictions = model(features)

                # Inverse transform predictions
                pred_np = trainer.scaler.inverse_transform(predictions.cpu().numpy())
                target_np = targets.cpu().numpy()

                all_predictions.append(pred_np)
                all_targets.append(target_np)

            except Exception as e:
                logger.error(f"Error in test batch: {e}")
                continue

    if not all_predictions:
        logger.error("No successful predictions made")
        return None

    predictions = np.concatenate(all_predictions, axis=0)
    targets = np.concatenate(all_targets, axis=0)

    # Calculate per-parameter metrics
    metrics_df = []
    for i, param_name in enumerate(param_names):
        pred_param = predictions[:, i]
        true_param = targets[:, i]

        mae = mean_absolute_error(true_param, pred_param)
        rmse = np.sqrt(mean_squared_error(true_param, pred_param))
        r2 = r2_score(true_param, pred_param)

        metrics_df.append({
            'Parameter': param_name,
            'MAE': mae,
            'RMSE': rmse,
            'R²': r2
        })

    metrics_df = pd.DataFrame(metrics_df)

    # Save metrics
    metrics_df.to_csv("cosmos_patient_split_evaluation_metrics.csv", index=False)
    logger.info("Saved detailed metrics to cosmos_patient_split_evaluation_metrics.csv")

    # Print summary
    logger.info("=" * 80)
    logger.info("🏥 PATIENT-AWARE COSMOS EVALUATION RESULTS 🏥")
    logger.info("=" * 80)

    # Sort by R² score for better readability
    metrics_sorted = metrics_df.sort_values('R²', ascending=False)

    logger.info("Per-parameter evaluation results (sorted by R² score):")
    logger.info("-" * 80)

    # Show top 10 best performing parameters
    logger.info("🏆 TOP 10 BEST PERFORMING PARAMETERS:")
    for i, (_, row) in enumerate(metrics_sorted.head(10).iterrows()):
        logger.info(f"{i+1:2d}. {row['Parameter']:20s}: MAE={row['MAE']:8.4f}, RMSE={row['RMSE']:8.4f}, R²={row['R²']:7.4f}")

    logger.info("-" * 80)

    # Show bottom 5 parameters
    logger.info("⚠️  BOTTOM 5 PARAMETERS (need improvement):")
    for i, (_, row) in enumerate(metrics_sorted.tail(5).iterrows()):
        logger.info(f"{i+1:2d}. {row['Parameter']:20s}: MAE={row['MAE']:8.4f}, RMSE={row['RMSE']:8.4f}, R²={row['R²']:7.4f}")

    # Overall metrics
    overall_mae = mean_absolute_error(targets, predictions)
    overall_rmse = np.sqrt(mean_squared_error(targets, predictions))
    overall_r2 = r2_score(targets, predictions)

    logger.info("-" * 80)
    logger.info(f"📊 OVERALL PERFORMANCE:")
    logger.info(f"   Overall MAE:  {overall_mae:8.4f}")
    logger.info(f"   Overall RMSE: {overall_rmse:8.4f}")
    logger.info(f"   Overall R²:   {overall_r2:7.4f}")

    # Performance summary
    positive_r2_count = (metrics_df['R²'] > 0).sum()
    good_r2_count = (metrics_df['R²'] > 0.1).sum()
    excellent_r2_count = (metrics_df['R²'] > 0.3).sum()

    logger.info("-" * 80)
    logger.info(f"📈 PERFORMANCE SUMMARY:")
    logger.info(f"   Parameters with positive R²: {positive_r2_count}/{len(param_names)} ({positive_r2_count/len(param_names)*100:.1f}%)")
    logger.info(f"   Parameters with R² > 0.1:    {good_r2_count}/{len(param_names)} ({good_r2_count/len(param_names)*100:.1f}%)")
    logger.info(f"   Parameters with R² > 0.3:    {excellent_r2_count}/{len(param_names)} ({excellent_r2_count/len(param_names)*100:.1f}%)")

    logger.info("=" * 80)

    return metrics_df, predictions, targets


if __name__ == "__main__":
    # Run patient-aware training
    result = main()
    if result is not None:
        logger.info("Patient-aware Cosmos feature extraction and training completed successfully!")

        # Load best model for evaluation
        checkpoint = torch.load("cosmos_patient_split_best_model.pth", weights_only=False)
        model = result['model']
        model.load_state_dict(checkpoint['model_state_dict'])
        model.to(result['config']['device'])

        trainer = result['trainer']
        trainer.scaler = checkpoint['scaler']

        # Evaluate model
        metrics_df, predictions, targets = evaluate_patient_split_model(
            model, trainer, result['test_loader'], result['param_names']
        )

        logger.info("🎉 Patient-aware Cosmos-based gait regression completed successfully!")
        logger.info("✅ No data leakage - patients are properly separated between train/val/test sets!")
    else:
        logger.error("Patient-aware Cosmos training failed!")
