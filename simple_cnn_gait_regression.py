#!/usr/bin/env python3
"""
Simple CNN-based Gait Regression Model

A simplified version using CNN for feature extraction instead of Cosmos tokenizer
to avoid dtype compatibility issues.
"""

import os
import sys
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt
from tqdm import tqdm
import cv2
import warnings
warnings.filterwarnings('ignore')

# Import data loading function
from gait_regression_model import load_and_preprocess_data

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SimpleCNNFeatureExtractor(nn.Module):
    """Simple CNN for video feature extraction."""
    
    def __init__(self, input_channels=3, feature_dim=512):
        super().__init__()
        
        # 3D CNN layers for temporal-spatial feature extraction
        self.conv3d_layers = nn.Sequential(
            # First 3D conv block
            nn.Conv3d(input_channels, 32, kernel_size=(3, 7, 7), stride=(1, 2, 2), padding=(1, 3, 3)),
            nn.BatchNorm3d(32),
            nn.ReLU(),
            nn.MaxPool3d(kernel_size=(1, 2, 2), stride=(1, 2, 2)),
            
            # Second 3D conv block
            nn.Conv3d(32, 64, kernel_size=(3, 5, 5), stride=(1, 2, 2), padding=(1, 2, 2)),
            nn.BatchNorm3d(64),
            nn.ReLU(),
            nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2)),
            
            # Third 3D conv block
            nn.Conv3d(64, 128, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(128),
            nn.ReLU(),
            nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2)),
        )
        
        # Global average pooling
        self.global_pool = nn.AdaptiveAvgPool3d((1, 1, 1))
        
        # Feature projection
        self.feature_proj = nn.Sequential(
            nn.Linear(128, feature_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        logger.info(f"Initialized CNN feature extractor with {feature_dim} features")
    
    def forward(self, x):
        """
        Forward pass.
        
        Args:
            x: Input tensor of shape (B, C, T, H, W)
        
        Returns:
            Features of shape (B, feature_dim)
        """
        # 3D convolutions
        x = self.conv3d_layers(x)
        
        # Global pooling
        x = self.global_pool(x)
        
        # Flatten
        x = x.view(x.size(0), -1)
        
        # Feature projection
        features = self.feature_proj(x)
        
        return features


class SimpleGaitVideoDataset(Dataset):
    """Simple dataset for gait videos."""
    
    def __init__(self, video_paths, gait_params, video_dir="data/videos/video_formated_trim", 
                 target_frames=16, target_size=(112, 112)):
        self.video_paths = video_paths
        self.gait_params = gait_params
        self.video_dir = video_dir
        self.target_frames = target_frames
        self.target_size = target_size
        
        logger.info(f"Dataset initialized with {len(video_paths)} videos")
    
    def __len__(self):
        return len(self.video_paths)
    
    def load_and_preprocess_video(self, video_path):
        """Load and preprocess a video."""
        full_path = os.path.join(self.video_dir, video_path)
        
        if not os.path.exists(full_path):
            # Return dummy video if file doesn't exist
            return np.random.randint(0, 255, (self.target_frames, *self.target_size, 3), dtype=np.uint8)
        
        try:
            # Read video using OpenCV
            cap = cv2.VideoCapture(full_path)
            frames = []
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Resize frame
                frame = cv2.resize(frame, self.target_size)
                # Convert BGR to RGB
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                frames.append(frame)
            
            cap.release()
            
            if len(frames) == 0:
                # Return dummy video if no frames
                return np.random.randint(0, 255, (self.target_frames, *self.target_size, 3), dtype=np.uint8)
            
            # Convert to numpy array
            video = np.array(frames)  # Shape: (T, H, W, C)
            
            # Sample or pad frames to target length
            if len(video) >= self.target_frames:
                # Sample frames uniformly
                indices = np.linspace(0, len(video) - 1, self.target_frames, dtype=int)
                video = video[indices]
            else:
                # Pad by repeating last frame
                padding_needed = self.target_frames - len(video)
                last_frame = video[-1:] if len(video) > 0 else np.zeros((1, *self.target_size, 3), dtype=np.uint8)
                padding = np.repeat(last_frame, padding_needed, axis=0)
                video = np.concatenate([video, padding], axis=0)
            
            return video
            
        except Exception as e:
            logger.warning(f"Error loading video {video_path}: {e}")
            # Return dummy video
            return np.random.randint(0, 255, (self.target_frames, *self.target_size, 3), dtype=np.uint8)
    
    def __getitem__(self, idx):
        video_path = self.video_paths[idx]
        gait_param = self.gait_params[idx]
        
        # Load video
        video = self.load_and_preprocess_video(video_path)  # Shape: (T, H, W, C)
        
        # Convert to tensor and normalize
        video = torch.from_numpy(video).float() / 255.0  # Normalize to [0, 1]
        video = video.permute(3, 0, 1, 2)  # Change to (C, T, H, W)
        
        # Convert gait parameters to tensor
        gait_param = torch.from_numpy(gait_param).float()
        
        return video, gait_param


class GaitRegressionModel(nn.Module):
    """Regression model for gait parameter prediction."""
    
    def __init__(self, feature_dim=512, num_gait_params=6, hidden_dims=[256, 128]):
        super().__init__()
        
        # Feature extractor
        self.feature_extractor = SimpleCNNFeatureExtractor(
            input_channels=3, 
            feature_dim=feature_dim
        )
        
        # Regression head
        layers = []
        prev_dim = feature_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.BatchNorm1d(hidden_dim)
            ])
            prev_dim = hidden_dim
        
        # Output layer
        layers.append(nn.Linear(prev_dim, num_gait_params))
        
        self.regressor = nn.Sequential(*layers)
        
        total_params = sum(p.numel() for p in self.parameters())
        logger.info(f"Initialized regression model with {total_params:,} parameters")
    
    def forward(self, x):
        # Extract features
        features = self.feature_extractor(x)
        
        # Predict gait parameters
        predictions = self.regressor(features)
        
        return predictions


class SimpleTrainer:
    """Simple trainer for the gait regression model."""
    
    def __init__(self, model, device="cuda", learning_rate=1e-3):
        self.device = device
        self.model = model.to(device)
        self.optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=1e-4)
        self.criterion = nn.MSELoss()
        
        self.train_losses = []
        self.val_losses = []
        
        logger.info(f"Initialized trainer on {device}")
    
    def train_epoch(self, dataloader):
        """Train for one epoch."""
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        for videos, targets in tqdm(dataloader, desc="Training"):
            try:
                videos = videos.to(self.device)
                targets = targets.to(self.device)
                
                # Forward pass
                self.optimizer.zero_grad()
                predictions = self.model(videos)
                loss = self.criterion(predictions, targets)
                
                # Backward pass
                loss.backward()
                self.optimizer.step()
                
                total_loss += loss.item()
                num_batches += 1
                
            except Exception as e:
                logger.error(f"Error in training batch: {e}")
                continue
        
        if num_batches > 0:
            avg_loss = total_loss / num_batches
            self.train_losses.append(avg_loss)
            return avg_loss
        else:
            return float('inf')
    
    def validate(self, dataloader):
        """Validate the model."""
        self.model.eval()
        total_loss = 0.0
        all_predictions = []
        all_targets = []
        num_batches = 0
        
        with torch.no_grad():
            for videos, targets in tqdm(dataloader, desc="Validating"):
                try:
                    videos = videos.to(self.device)
                    targets = targets.to(self.device)
                    
                    # Forward pass
                    predictions = self.model(videos)
                    loss = self.criterion(predictions, targets)
                    
                    total_loss += loss.item()
                    num_batches += 1
                    
                    # Store for metrics
                    all_predictions.append(predictions.cpu().numpy())
                    all_targets.append(targets.cpu().numpy())
                    
                except Exception as e:
                    logger.error(f"Error in validation batch: {e}")
                    continue
        
        if num_batches > 0:
            avg_loss = total_loss / num_batches
            self.val_losses.append(avg_loss)
            
            # Calculate metrics
            if all_predictions and all_targets:
                predictions = np.concatenate(all_predictions, axis=0)
                targets = np.concatenate(all_targets, axis=0)
                
                r2 = r2_score(targets, predictions)
                mae = mean_absolute_error(targets, predictions)
                
                return avg_loss, {'r2': r2, 'mae': mae}
            else:
                return avg_loss, {'r2': 0.0, 'mae': float('inf')}
        else:
            return float('inf'), {'r2': 0.0, 'mae': float('inf')}
    
    def fit(self, train_loader, val_loader, epochs=10):
        """Train the model."""
        logger.info(f"Starting training for {epochs} epochs...")
        
        best_val_loss = float('inf')
        
        for epoch in range(epochs):
            # Train
            train_loss = self.train_epoch(train_loader)
            
            # Validate
            val_loss, metrics = self.validate(val_loader)
            
            # Log progress
            logger.info(f"Epoch {epoch+1}/{epochs}: "
                       f"Train Loss: {train_loss:.4f}, "
                       f"Val Loss: {val_loss:.4f}, "
                       f"Val R²: {metrics['r2']:.4f}")
            
            # Save best model
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                torch.save(self.model.state_dict(), "simple_cnn_best_model.pth")
                logger.info(f"Saved best model with validation loss: {val_loss:.4f}")
        
        return {
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'best_val_loss': best_val_loss
        }


def main():
    """Main training function."""
    logger.info("Starting Simple CNN Gait Regression Training")
    
    # Configuration
    config = {
        'device': 'cuda' if torch.cuda.is_available() else 'cpu',
        'batch_size': 4,
        'epochs': 10,
        'learning_rate': 1e-3,
        'subset_size': 100,  # Use subset for testing
        'test_size': 0.2,
        'val_size': 0.2,
        'target_frames': 16,
        'target_size': (112, 112)
    }
    
    logger.info(f"Configuration: {config}")
    
    try:
        # Load data
        logger.info("Loading data...")
        video_paths, gait_params, param_names = load_and_preprocess_data()
        
        # Use subset for testing
        subset_size = min(config['subset_size'], len(video_paths))
        video_paths = video_paths[:subset_size]
        gait_params = gait_params[:subset_size]
        
        logger.info(f"Using subset of {subset_size} samples")
        
        # Create data splits
        train_val_paths, test_paths, train_val_params, test_params = train_test_split(
            video_paths, gait_params, test_size=config['test_size'], random_state=42
        )
        
        train_paths, val_paths, train_params, val_params = train_test_split(
            train_val_paths, train_val_params, test_size=config['val_size'], random_state=42
        )
        
        logger.info(f"Data split: Train={len(train_paths)}, Val={len(val_paths)}, Test={len(test_paths)}")
        
        # Create datasets and loaders
        train_dataset = SimpleGaitVideoDataset(
            train_paths, train_params, 
            target_frames=config['target_frames'],
            target_size=config['target_size']
        )
        val_dataset = SimpleGaitVideoDataset(
            val_paths, val_params,
            target_frames=config['target_frames'],
            target_size=config['target_size']
        )
        
        train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True, num_workers=2)
        val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=2)
        
        # Initialize model
        logger.info("Initializing model...")
        model = GaitRegressionModel(
            feature_dim=512,
            num_gait_params=len(param_names),
            hidden_dims=[256, 128]
        )
        
        # Initialize trainer
        trainer = SimpleTrainer(
            model=model,
            device=config['device'],
            learning_rate=config['learning_rate']
        )
        
        # Train
        logger.info("Starting training...")
        history = trainer.fit(
            train_loader=train_loader,
            val_loader=val_loader,
            epochs=config['epochs']
        )
        
        # Plot results
        plt.figure(figsize=(12, 4))
        
        plt.subplot(1, 2, 1)
        plt.plot(history['train_losses'], label='Train Loss', marker='o')
        plt.plot(history['val_losses'], label='Val Loss', marker='s')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Training History')
        plt.legend()
        plt.grid(True)
        
        plt.subplot(1, 2, 2)
        plt.plot(history['train_losses'], label='Train Loss', marker='o')
        plt.plot(history['val_losses'], label='Val Loss', marker='s')
        plt.xlabel('Epoch')
        plt.ylabel('Loss (Log)')
        plt.title('Training History (Log Scale)')
        plt.yscale('log')
        plt.legend()
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('simple_cnn_training_results.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        logger.info("Training completed successfully!")
        logger.info(f"Best validation loss: {history['best_val_loss']:.4f}")
        logger.info("Results saved to simple_cnn_training_results.png")
        
        return history
        
    except Exception as e:
        logger.error(f"Error in training: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    main()
