#!/usr/bin/env python3
"""
Enhanced Cosmos Video Tokenizer Based Gait Parameter Regression

This script implements an enhanced high-performance gait parameter regression model using
Cosmos Video Tokenizer's pre-trained video representations with multiple performance improvements:

1. Larger dataset usage (full dataset instead of subset)
2. Enhanced model architecture with attention and residual connections
3. Advanced training techniques (cosine annealing, warmup, gradient accumulation)
4. Data augmentation and regularization
5. Multi-scale feature extraction
6. Ensemble predictions
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import cv2
import pickle
import warnings
import math
warnings.filterwarnings('ignore')

# Import Cosmos tokenizer
from cosmos_tokenizer.video_lib import CausalVideoTokenizer

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def extract_patient_id_from_filename(filename):
    """Extract patient ID from video filename."""
    try:
        basename = os.path.basename(filename)
        patient_id = basename.split('_')[0]
        return patient_id
    except:
        return None


def patient_aware_data_split(df, test_size=0.2, val_size=0.2, random_state=42):
    """Split data by patient ID to prevent data leakage."""
    logger.info("Performing patient-aware data splitting...")
    
    if 'Pt_id' in df.columns:
        patient_ids = df['Pt_id'].unique()
        logger.info(f"Using Pt_id column: {len(patient_ids)} unique patients")
    else:
        df['extracted_patient_id'] = df['VideoFile'].apply(extract_patient_id_from_filename)
        patient_ids = df['extracted_patient_id'].unique()
        logger.info(f"Extracted patient IDs from filenames: {len(patient_ids)} unique patients")
    
    patient_ids = [pid for pid in patient_ids if pid is not None]
    logger.info(f"Valid patient IDs: {len(patient_ids)}")
    
    # Split patients into train/val/test
    train_val_patients, test_patients = train_test_split(
        patient_ids, test_size=test_size, random_state=random_state
    )
    
    train_patients, val_patients = train_test_split(
        train_val_patients, test_size=val_size, random_state=random_state
    )
    
    logger.info(f"Patient split: Train={len(train_patients)}, Val={len(val_patients)}, Test={len(test_patients)}")
    
    # Create data splits based on patient assignment
    if 'Pt_id' in df.columns:
        train_df = df[df['Pt_id'].isin(train_patients)].copy()
        val_df = df[df['Pt_id'].isin(val_patients)].copy()
        test_df = df[df['Pt_id'].isin(test_patients)].copy()
    else:
        train_df = df[df['extracted_patient_id'].isin(train_patients)].copy()
        val_df = df[df['extracted_patient_id'].isin(val_patients)].copy()
        test_df = df[df['extracted_patient_id'].isin(test_patients)].copy()
    
    logger.info(f"Sample split: Train={len(train_df)}, Val={len(val_df)}, Test={len(test_df)}")
    
    # Verify no patient overlap
    if 'Pt_id' in df.columns:
        train_pts = set(train_df['Pt_id'].unique())
        val_pts = set(val_df['Pt_id'].unique())
        test_pts = set(test_df['Pt_id'].unique())
    else:
        train_pts = set(train_df['extracted_patient_id'].unique())
        val_pts = set(val_df['extracted_patient_id'].unique())
        test_pts = set(test_df['extracted_patient_id'].unique())
    
    # Check for overlaps
    train_val_overlap = train_pts.intersection(val_pts)
    train_test_overlap = train_pts.intersection(test_pts)
    val_test_overlap = val_pts.intersection(test_pts)
    
    if train_val_overlap or train_test_overlap or val_test_overlap:
        logger.error(f"Patient overlap detected!")
        raise ValueError("Patient overlap detected in data splits!")
    else:
        logger.info("✅ No patient overlap detected - data splits are clean!")
    
    return train_df, val_df, test_df


def load_and_preprocess_data_enhanced(data_path="data/gaitrite_full_dataset.xlsx",
                                    video_dir="data/videos/video_formated_trim",
                                    test_size=0.2, val_size=0.2, random_state=42,
                                    use_full_dataset=True):
    """Load and preprocess the gait dataset with enhanced features."""
    logger.info("Loading enhanced gait dataset with patient-aware splitting...")
    
    # Load the dataset
    df = pd.read_excel(data_path)
    logger.info(f"Loaded dataset with {len(df)} samples and {len(df.columns)} columns")
    
    # Find Velocity column index and get all columns from Velocity onwards
    velocity_idx = df.columns.get_loc('Velocity')
    target_params = df.columns[velocity_idx:].tolist()
    
    logger.info(f"Target parameters ({len(target_params)}): {target_params}")
    
    # Filter valid video files
    valid_rows = []
    for idx, row in df.iterrows():
        video_file = row['VideoFile']
        if pd.isna(video_file):
            continue
            
        filename = os.path.basename(video_file)
        video_path = os.path.join(video_dir, filename)
        
        if os.path.exists(video_path):
            valid_rows.append(idx)
    
    # Keep only valid rows
    df_valid = df.iloc[valid_rows].copy()
    logger.info(f"Found {len(df_valid)} samples with valid video files")
    
    # Remove samples with missing gait parameters
    gait_data = df_valid[target_params].values
    missing_mask = np.isnan(gait_data).any(axis=1)
    if missing_mask.any():
        logger.warning(f"Removing {missing_mask.sum()} samples with missing gait parameters")
        df_valid = df_valid[~missing_mask].copy()
    
    logger.info(f"Final valid dataset: {len(df_valid)} samples")
    
    # Use full dataset if specified
    if use_full_dataset:
        logger.info("🚀 Using FULL DATASET for enhanced performance!")
    else:
        # Use subset for testing
        subset_size = min(2000, len(df_valid))
        df_valid = df_valid.sample(n=subset_size, random_state=random_state).copy()
        logger.info(f"Using subset of {subset_size} samples for testing")
    
    # Perform patient-aware splitting
    train_df, val_df, test_df = patient_aware_data_split(
        df_valid, test_size=test_size, val_size=val_size, random_state=random_state
    )
    
    # Extract data for each split
    def extract_split_data(split_df):
        video_paths = []
        gait_params = []
        
        for _, row in split_df.iterrows():
            filename = os.path.basename(row['VideoFile'])
            video_paths.append(filename)
            gait_params.append(row[target_params].values)
        
        return video_paths, np.array(gait_params, dtype=np.float32)
    
    train_paths, train_params = extract_split_data(train_df)
    val_paths, val_params = extract_split_data(val_df)
    test_paths, test_params = extract_split_data(test_df)
    
    logger.info(f"Enhanced splits - Train: {len(train_paths)}, Val: {len(val_paths)}, Test: {len(test_paths)}")
    
    return (train_paths, train_params), (val_paths, val_params), (test_paths, test_params), target_params


class CosmosFeatureExtractorEnhanced:
    """Enhanced Cosmos Video Tokenizer based feature extractor with multi-scale features."""
    
    def __init__(self, 
                 model_name="Cosmos-1.0-Tokenizer-CV8x8x8",
                 device="cuda",
                 dtype="bfloat16",
                 temporal_window=17,
                 multi_scale=True):
        """Initialize enhanced Cosmos feature extractor."""
        self.model_name = model_name
        self.device = device
        self.dtype = dtype
        self.temporal_window = temporal_window
        self.multi_scale = multi_scale
        
        # Initialize Cosmos tokenizer
        encoder_ckpt = f"pretrained_ckpts/{model_name}/encoder.jit"
        
        if not os.path.exists(encoder_ckpt):
            raise FileNotFoundError(f"Encoder checkpoint not found: {encoder_ckpt}")
        
        logger.info(f"Loading enhanced Cosmos tokenizer: {model_name}")
        self.tokenizer = CausalVideoTokenizer(
            checkpoint_enc=encoder_ckpt,
            device=device,
            dtype=dtype,
        )
        
        logger.info(f"Successfully initialized enhanced {model_name} feature extractor")
    
    def load_and_preprocess_video_enhanced(self, video_path, target_sizes=[(224, 224), (256, 256)]):
        """Load and preprocess a single video with multi-scale support."""
        try:
            cap = cv2.VideoCapture(video_path)
            frames = []
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Convert to RGB first
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                frames.append(frame)
            
            cap.release()
            
            if len(frames) == 0:
                logger.warning(f"No frames found in {video_path}")
                return None
            
            # Convert to numpy array
            video = np.array(frames)  # (T, H, W, 3)
            
            # Sample frames to temporal window size
            if len(video) >= self.temporal_window:
                indices = np.linspace(0, len(video) - 1, self.temporal_window, dtype=int)
                video = video[indices]
            else:
                # Pad with last frame if too short
                padding_needed = self.temporal_window - len(video)
                last_frame = video[-1:] if len(video) > 0 else np.zeros((1, 224, 224, 3), dtype=np.uint8)
                padding = np.repeat(last_frame, padding_needed, axis=0)
                video = np.concatenate([video, padding], axis=0)
            
            # Multi-scale processing
            if self.multi_scale:
                processed_videos = []
                for target_size in target_sizes:
                    resized_video = np.array([cv2.resize(frame, target_size) for frame in video])
                    processed_videos.append(resized_video)
                return processed_videos
            else:
                # Single scale
                target_size = target_sizes[0]
                resized_video = np.array([cv2.resize(frame, target_size) for frame in video])
                return [resized_video]
            
        except Exception as e:
            logger.error(f"Error loading video {video_path}: {e}")
            return None
    
    def extract_features_enhanced(self, videos):
        """Extract enhanced features from videos using Cosmos tokenizer."""
        if videos is None:
            return None
        
        try:
            all_features = []
            
            for video in videos:
                # Convert to tensor format expected by Cosmos
                # Input: (T, H, W, 3) -> (1, 3, T, H, W)
                video_tensor = torch.from_numpy(video).float()
                video_tensor = video_tensor.permute(3, 0, 1, 2).unsqueeze(0)  # (1, 3, T, H, W)
                
                # Normalize to [-1, 1] range
                video_tensor = (video_tensor / 255.0) * 2.0 - 1.0
                
                # Move to device and convert dtype
                video_tensor = video_tensor.to(self.device)
                if self.dtype == "bfloat16":
                    video_tensor = video_tensor.to(torch.bfloat16)
                
                # Extract features using Cosmos encoder
                with torch.no_grad():
                    latent_features = self.tokenizer.encode(video_tensor)
                    if isinstance(latent_features, tuple):
                        latent_features = latent_features[0]
                
                # Convert back to float32 for downstream processing
                latent_features = latent_features.float().cpu()
                
                # Flatten the features
                features_flat = latent_features.flatten().numpy()
                all_features.append(features_flat)
            
            # Concatenate multi-scale features
            if len(all_features) > 1:
                combined_features = np.concatenate(all_features)
            else:
                combined_features = all_features[0]
            
            return combined_features
            
        except Exception as e:
            logger.error(f"Error extracting enhanced features: {e}")
            return None
    
    def extract_and_save_features_enhanced(self, video_paths, video_dir, save_path):
        """Extract enhanced features for all videos and save to disk."""
        logger.info(f"Extracting enhanced Cosmos features for {len(video_paths)} videos...")
        
        all_features = []
        valid_indices = []
        
        for i, video_filename in enumerate(tqdm(video_paths, desc="Extracting enhanced features")):
            video_path = os.path.join(video_dir, video_filename)
            
            # Load and preprocess video with multi-scale
            videos = self.load_and_preprocess_video_enhanced(video_path)
            
            # Extract enhanced features
            features = self.extract_features_enhanced(videos)
            
            if features is not None:
                all_features.append(features)
                valid_indices.append(i)
            else:
                logger.warning(f"Failed to extract features for {video_filename}")
        
        if all_features:
            # Convert to numpy array
            features_array = np.array(all_features)
            
            # Save features and valid indices
            save_data = {
                'features': features_array,
                'valid_indices': valid_indices,
                'feature_shape': features_array.shape,
                'model_name': self.model_name,
                'multi_scale': self.multi_scale
            }
            
            with open(save_path, 'wb') as f:
                pickle.dump(save_data, f)
            
            logger.info(f"Saved {len(all_features)} enhanced feature vectors to {save_path}")
            logger.info(f"Enhanced feature shape: {features_array.shape}")
            
            return features_array, valid_indices
        else:
            logger.error("No enhanced features were successfully extracted!")
            return None, None


class CosmosLatentDatasetEnhanced(Dataset):
    """Enhanced dataset for pre-extracted Cosmos latent features with augmentation."""

    def __init__(self, features, gait_params, augment=False):
        """Initialize enhanced dataset with optional augmentation."""
        self.features = features
        self.gait_params = gait_params
        self.augment = augment

        logger.info(f"Initialized enhanced Cosmos latent dataset with {len(features)} samples")
        logger.info(f"Feature dimension: {features.shape[1]}")
        logger.info(f"Number of gait parameters: {gait_params.shape[1]}")
        logger.info(f"Augmentation: {'Enabled' if augment else 'Disabled'}")

    def __len__(self):
        return len(self.features)

    def __getitem__(self, idx):
        features = torch.from_numpy(self.features[idx]).float()
        gait_params = torch.from_numpy(self.gait_params[idx]).float()

        # Apply augmentation if enabled
        if self.augment:
            # Add small amount of noise to features
            noise_std = 0.01
            features += torch.randn_like(features) * noise_std

            # Small random scaling
            scale_factor = torch.normal(1.0, 0.02, size=(1,))
            features *= scale_factor

        return features, gait_params


class MultiHeadAttention(nn.Module):
    """Multi-head attention mechanism for feature enhancement."""

    def __init__(self, d_model, num_heads=8, dropout=0.1):
        super().__init__()
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads

        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)

        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(d_model)

    def forward(self, x):
        batch_size = x.size(0)

        # Add sequence dimension for attention
        x = x.unsqueeze(1)  # (batch, 1, features)

        # Self-attention
        q = self.w_q(x).view(batch_size, 1, self.num_heads, self.d_k).transpose(1, 2)
        k = self.w_k(x).view(batch_size, 1, self.num_heads, self.d_k).transpose(1, 2)
        v = self.w_v(x).view(batch_size, 1, self.num_heads, self.d_k).transpose(1, 2)

        # Attention scores
        scores = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(self.d_k)
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)

        # Apply attention
        attn_output = torch.matmul(attn_weights, v)
        attn_output = attn_output.transpose(1, 2).contiguous().view(batch_size, 1, self.d_model)
        attn_output = self.w_o(attn_output)

        # Residual connection and layer norm
        output = self.layer_norm(x + attn_output)

        return output.squeeze(1)  # Remove sequence dimension


class EnhancedCosmosRegressionModel(nn.Module):
    """Enhanced regression model with attention, residual connections, and advanced regularization."""

    def __init__(self, feature_dim, num_gait_params=28,
                 hidden_dims=[2048, 1024, 512, 256],
                 use_attention=True,
                 dropout_rate=0.3,
                 use_batch_norm=True):
        """Initialize enhanced regression model."""
        super().__init__()

        self.feature_dim = feature_dim
        self.num_gait_params = num_gait_params
        self.use_attention = use_attention

        # Input projection
        self.input_projection = nn.Linear(feature_dim, hidden_dims[0])

        # Multi-head attention
        if use_attention:
            self.attention = MultiHeadAttention(hidden_dims[0], num_heads=8, dropout=dropout_rate)

        # Build enhanced regression network with residual connections
        self.layers = nn.ModuleList()
        prev_dim = hidden_dims[0]

        for i, hidden_dim in enumerate(hidden_dims[1:]):
            layer_block = nn.ModuleList([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(dropout_rate),
            ])

            if use_batch_norm:
                layer_block.append(nn.BatchNorm1d(hidden_dim))

            # Add residual connection if dimensions match
            if prev_dim == hidden_dim:
                layer_block.append(nn.Identity())  # Placeholder for residual

            self.layers.append(layer_block)
            prev_dim = hidden_dim

        # Output layers with multiple heads for different parameter groups
        self.output_layers = nn.ModuleDict({
            'velocity_group': nn.Linear(prev_dim, 4),  # Velocity, StrideVelocity_L/R, Cadence
            'spatial_group': nn.Linear(prev_dim, 8),   # Stride lengths, support base, toe angles
            'temporal_group': nn.Linear(prev_dim, 8),  # Cycle times, stride times, percentages
            'variability_group': nn.Linear(prev_dim, 8)  # SD and CV parameters
        })

        # Final combination layer
        self.final_layer = nn.Linear(prev_dim, num_gait_params)

        total_params = sum(p.numel() for p in self.parameters())
        logger.info(f"Initialized enhanced Cosmos regression model with {total_params:,} parameters")
        logger.info(f"Architecture: {hidden_dims}, Attention: {use_attention}, BatchNorm: {use_batch_norm}")

    def forward(self, x):
        # Input projection
        x = self.input_projection(x)
        x = F.relu(x)

        # Apply attention if enabled
        if self.use_attention:
            x = self.attention(x)

        # Forward through layers with residual connections
        for i, layer_block in enumerate(self.layers):
            residual = x

            # Apply linear, activation, dropout
            for j, layer in enumerate(layer_block):
                if isinstance(layer, nn.Identity):
                    # Add residual connection
                    x = x + residual
                else:
                    x = layer(x)

        # Multi-head output (experimental)
        # velocity_out = self.output_layers['velocity_group'](x)
        # spatial_out = self.output_layers['spatial_group'](x)
        # temporal_out = self.output_layers['temporal_group'](x)
        # variability_out = self.output_layers['variability_group'](x)
        #
        # # Combine outputs
        # combined_out = torch.cat([velocity_out, spatial_out, temporal_out, variability_out], dim=1)

        # Simple final layer for now
        output = self.final_layer(x)

        return output


class CosineAnnealingWarmupRestarts(optim.lr_scheduler._LRScheduler):
    """Cosine annealing with warm restarts scheduler."""

    def __init__(self, optimizer, first_cycle_steps, cycle_mult=1., max_lr=0.1, min_lr=0.001,
                 warmup_steps=0, gamma=1., last_epoch=-1):
        self.first_cycle_steps = first_cycle_steps
        self.cycle_mult = cycle_mult
        self.base_max_lr = max_lr
        self.max_lr = max_lr
        self.min_lr = min_lr
        self.warmup_steps = warmup_steps
        self.gamma = gamma

        self.cur_cycle_steps = first_cycle_steps
        self.cycle = 0
        self.step_in_cycle = last_epoch

        super(CosineAnnealingWarmupRestarts, self).__init__(optimizer, last_epoch)

        self.init_lr()

    def init_lr(self):
        self.base_lrs = []
        for param_group in self.optimizer.param_groups:
            param_group['lr'] = self.min_lr
            self.base_lrs.append(self.min_lr)

    def get_lr(self):
        if self.step_in_cycle == -1:
            return self.base_lrs
        elif self.step_in_cycle < self.warmup_steps:
            return [(self.max_lr - base_lr)*self.step_in_cycle / self.warmup_steps + base_lr for base_lr in self.base_lrs]
        else:
            return [base_lr + (self.max_lr - base_lr) \
                    * (1 + math.cos(math.pi * (self.step_in_cycle-self.warmup_steps) \
                                    / (self.cur_cycle_steps - self.warmup_steps))) / 2
                    for base_lr in self.base_lrs]

    def step(self, epoch=None):
        if epoch is None:
            epoch = self.last_epoch + 1
            self.step_in_cycle = self.step_in_cycle + 1
            if self.step_in_cycle >= self.cur_cycle_steps:
                self.cycle += 1
                self.step_in_cycle = self.step_in_cycle - self.cur_cycle_steps
                self.cur_cycle_steps = int((self.cur_cycle_steps - self.warmup_steps) * self.cycle_mult) + self.warmup_steps
        else:
            if epoch >= self.first_cycle_steps:
                if self.cycle_mult == 1.:
                    self.step_in_cycle = epoch % self.first_cycle_steps
                    self.cycle = epoch // self.first_cycle_steps
                else:
                    n = int(math.log((epoch / self.first_cycle_steps * (self.cycle_mult - 1) + 1), self.cycle_mult))
                    self.cycle = n
                    self.step_in_cycle = epoch - int(self.first_cycle_steps * (self.cycle_mult ** n - 1) / (self.cycle_mult - 1))
                    self.cur_cycle_steps = self.first_cycle_steps * self.cycle_mult ** (n)
            else:
                self.cur_cycle_steps = self.first_cycle_steps
                self.step_in_cycle = epoch

        self.max_lr = self.base_max_lr * (self.gamma**self.cycle)
        self.last_epoch = math.floor(epoch)
        for param_group, lr in zip(self.optimizer.param_groups, self.get_lr()):
            param_group['lr'] = lr


class EnhancedCosmosTrainer:
    """Enhanced trainer with advanced optimization techniques."""

    def __init__(self, model, device="cuda", learning_rate=1e-3, weight_decay=1e-4,
                 gradient_accumulation_steps=1, use_cosine_annealing=True,
                 use_robust_scaler=True, warmup_epochs=5):
        self.device = device
        self.model = model.to(device)
        self.gradient_accumulation_steps = gradient_accumulation_steps
        self.use_cosine_annealing = use_cosine_annealing
        self.warmup_epochs = warmup_epochs

        # Enhanced optimizer
        self.optimizer = optim.AdamW(
            model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay,
            betas=(0.9, 0.999),
            eps=1e-8
        )

        # Enhanced loss function with Huber loss for robustness
        self.criterion = nn.HuberLoss(delta=1.0)

        # Enhanced scaler
        if use_robust_scaler:
            self.scaler = RobustScaler()
        else:
            self.scaler = StandardScaler()
        self.target_fitted = False

        self.train_losses = []
        self.val_losses = []
        self.learning_rates = []
        self.best_val_loss = float('inf')
        self.best_val_r2 = -float('inf')

        logger.info(f"Initialized enhanced Cosmos trainer on {device}")
        logger.info(f"Gradient accumulation steps: {gradient_accumulation_steps}")
        logger.info(f"Cosine annealing: {use_cosine_annealing}")
        logger.info(f"Robust scaler: {use_robust_scaler}")

    def setup_scheduler(self, total_epochs, steps_per_epoch):
        """Setup learning rate scheduler."""
        if self.use_cosine_annealing:
            total_steps = total_epochs * steps_per_epoch
            warmup_steps = self.warmup_epochs * steps_per_epoch

            self.scheduler = CosineAnnealingWarmupRestarts(
                self.optimizer,
                first_cycle_steps=total_steps,
                max_lr=self.optimizer.param_groups[0]['lr'],
                min_lr=self.optimizer.param_groups[0]['lr'] * 0.01,
                warmup_steps=warmup_steps
            )
        else:
            self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer, patience=10, factor=0.5, verbose=True
            )

    def fit_scaler(self, targets):
        """Fit the scaler on training targets."""
        if not self.target_fitted:
            self.scaler.fit(targets)
            self.target_fitted = True
            logger.info("Fitted enhanced target scaler")

    def train_epoch(self, dataloader):
        """Enhanced training for one epoch with gradient accumulation."""
        self.model.train()
        total_loss = 0.0
        num_batches = 0

        self.optimizer.zero_grad()

        for batch_idx, (features, targets) in enumerate(tqdm(dataloader, desc="Enhanced Training")):
            try:
                features = features.to(self.device)
                targets = targets.to(self.device)

                # Standardize targets
                targets_np = targets.cpu().numpy()
                targets_scaled = self.scaler.transform(targets_np)
                targets = torch.from_numpy(targets_scaled).float().to(self.device)

                predictions = self.model(features)
                loss = self.criterion(predictions, targets)

                # Scale loss for gradient accumulation
                loss = loss / self.gradient_accumulation_steps
                loss.backward()

                # Gradient accumulation
                if (batch_idx + 1) % self.gradient_accumulation_steps == 0:
                    # Gradient clipping
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                    self.optimizer.step()

                    if self.use_cosine_annealing:
                        self.scheduler.step()

                    self.optimizer.zero_grad()

                total_loss += loss.item() * self.gradient_accumulation_steps
                num_batches += 1

            except Exception as e:
                logger.error(f"Error in enhanced training batch: {e}")
                continue

        # Handle remaining gradients
        if num_batches % self.gradient_accumulation_steps != 0:
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            self.optimizer.step()
            self.optimizer.zero_grad()

        if num_batches > 0:
            avg_loss = total_loss / num_batches
            self.train_losses.append(avg_loss)
            return avg_loss
        else:
            return float('inf')

    def validate(self, dataloader):
        """Enhanced validation with multiple metrics."""
        self.model.eval()
        total_loss = 0.0
        all_predictions = []
        all_targets = []
        num_batches = 0

        with torch.no_grad():
            for features, targets in tqdm(dataloader, desc="Enhanced Validating"):
                try:
                    features = features.to(self.device)
                    targets = targets.to(self.device)

                    # Standardize targets
                    targets_np = targets.cpu().numpy()
                    targets_scaled = self.scaler.transform(targets_np)
                    targets_scaled_tensor = torch.from_numpy(targets_scaled).float().to(self.device)

                    predictions = self.model(features)
                    loss = self.criterion(predictions, targets_scaled_tensor)

                    total_loss += loss.item()
                    num_batches += 1

                    # Inverse transform for metrics calculation
                    pred_np = self.scaler.inverse_transform(predictions.cpu().numpy())
                    all_predictions.append(pred_np)
                    all_targets.append(targets_np)

                except Exception as e:
                    logger.error(f"Error in enhanced validation batch: {e}")
                    continue

        if num_batches > 0:
            avg_loss = total_loss / num_batches
            self.val_losses.append(avg_loss)

            if all_predictions and all_targets:
                predictions = np.concatenate(all_predictions, axis=0)
                targets = np.concatenate(all_targets, axis=0)

                r2 = r2_score(targets, predictions)
                mae = mean_absolute_error(targets, predictions)
                rmse = np.sqrt(mean_squared_error(targets, predictions))

                # Calculate per-parameter R² for top parameters
                param_r2_scores = []
                for i in range(targets.shape[1]):
                    param_r2 = r2_score(targets[:, i], predictions[:, i])
                    param_r2_scores.append(param_r2)

                top_5_r2 = sorted(param_r2_scores, reverse=True)[:5]
                avg_top_5_r2 = np.mean(top_5_r2)

                return avg_loss, {
                    'r2': r2,
                    'mae': mae,
                    'rmse': rmse,
                    'top_5_r2': avg_top_5_r2,
                    'param_r2_scores': param_r2_scores
                }
            else:
                return avg_loss, {
                    'r2': 0.0,
                    'mae': float('inf'),
                    'rmse': float('inf'),
                    'top_5_r2': 0.0,
                    'param_r2_scores': []
                }
        else:
            return float('inf'), {
                'r2': 0.0,
                'mae': float('inf'),
                'rmse': float('inf'),
                'top_5_r2': 0.0,
                'param_r2_scores': []
            }

    def fit(self, train_loader, val_loader, epochs=100, save_path="cosmos_enhanced_best_model.pth"):
        """Enhanced training with advanced techniques."""
        logger.info(f"Starting enhanced patient-aware Cosmos training for {epochs} epochs...")

        # Setup scheduler
        self.setup_scheduler(epochs, len(train_loader))

        # Fit scaler on training data
        all_targets = []
        for _, targets in train_loader:
            all_targets.append(targets.numpy())
        all_targets = np.concatenate(all_targets, axis=0)
        self.fit_scaler(all_targets)

        # Training loop
        for epoch in range(epochs):
            train_loss = self.train_epoch(train_loader)
            val_loss, metrics = self.validate(val_loader)

            # Update learning rate (for non-cosine schedulers)
            if not self.use_cosine_annealing:
                self.scheduler.step(val_loss)

            current_lr = self.optimizer.param_groups[0]['lr']
            self.learning_rates.append(current_lr)

            logger.info(f"Epoch {epoch+1}/{epochs}: "
                       f"Train Loss: {train_loss:.4f}, "
                       f"Val Loss: {val_loss:.4f}, "
                       f"Val R²: {metrics['r2']:.4f}, "
                       f"Top-5 R²: {metrics['top_5_r2']:.4f}, "
                       f"Val MAE: {metrics['mae']:.4f}, "
                       f"Val RMSE: {metrics['rmse']:.4f}, "
                       f"LR: {current_lr:.6f}")

            # Save best model based on multiple criteria
            is_best = False
            if val_loss < self.best_val_loss:
                self.best_val_loss = val_loss
                is_best = True

            if metrics['r2'] > self.best_val_r2:
                self.best_val_r2 = metrics['r2']
                is_best = True

            if is_best:
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'scaler': self.scaler,
                    'val_loss': val_loss,
                    'metrics': metrics,
                    'config': {
                        'gradient_accumulation_steps': self.gradient_accumulation_steps,
                        'use_cosine_annealing': self.use_cosine_annealing,
                        'warmup_epochs': self.warmup_epochs
                    }
                }, save_path)
                logger.info(f"💾 Saved enhanced best model - Val Loss: {val_loss:.4f}, Val R²: {metrics['r2']:.4f}")

        return {
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'learning_rates': self.learning_rates,
            'best_val_loss': self.best_val_loss,
            'best_val_r2': self.best_val_r2
        }


def main_enhanced():
    """Enhanced main function with full dataset and advanced techniques."""
    logger.info("🚀 Starting ENHANCED Patient-Aware Cosmos-based Gait Parameter Regression")

    config = {
        'device': 'cuda' if torch.cuda.is_available() else 'cpu',
        'model_name': 'Cosmos-1.0-Tokenizer-CV8x8x8',
        'dtype': 'bfloat16',
        'temporal_window': 17,
        'batch_size': 8,  # Smaller batch size for larger model
        'epochs': 100,    # More epochs for better convergence
        'learning_rate': 2e-4,  # Lower learning rate for stability
        'weight_decay': 1e-5,   # Reduced weight decay
        'gradient_accumulation_steps': 4,  # Effective batch size = 8 * 4 = 32
        'use_full_dataset': True,  # 🔥 Use full dataset!
        'test_size': 0.2,
        'val_size': 0.2,
        'features_cache_path': 'cosmos_enhanced_features_cache.pkl',
        'use_attention': True,
        'use_cosine_annealing': True,
        'use_robust_scaler': True,
        'warmup_epochs': 10,
        'multi_scale': True
    }

    logger.info(f"🔧 Enhanced Configuration: {config}")

    try:
        # Load data with enhanced preprocessing
        (train_paths, train_params), (val_paths, val_params), (test_paths, test_params), param_names = \
            load_and_preprocess_data_enhanced(
                test_size=config['test_size'],
                val_size=config['val_size'],
                random_state=42,
                use_full_dataset=config['use_full_dataset']
            )

        logger.info(f"📊 Enhanced dataset - Train: {len(train_paths)}, Val: {len(val_paths)}, Test: {len(test_paths)}")

        # Check if enhanced features are already cached
        if os.path.exists(config['features_cache_path']):
            logger.info("📁 Loading cached enhanced Cosmos features...")
            with open(config['features_cache_path'], 'rb') as f:
                cache_data = pickle.load(f)

            # Extract features for each split
            all_paths = train_paths + val_paths + test_paths
            train_features = cache_data['features'][:len(train_paths)]
            val_features = cache_data['features'][len(train_paths):len(train_paths)+len(val_paths)]
            test_features = cache_data['features'][len(train_paths)+len(val_paths):]

            logger.info(f"✅ Loaded cached enhanced features - Train: {train_features.shape}, Val: {val_features.shape}, Test: {test_features.shape}")
        else:
            # Extract enhanced features using Cosmos
            logger.info("🎯 Extracting enhanced features using Cosmos tokenizer...")
            feature_extractor = CosmosFeatureExtractorEnhanced(
                model_name=config['model_name'],
                device=config['device'],
                dtype=config['dtype'],
                temporal_window=config['temporal_window'],
                multi_scale=config['multi_scale']
            )

            # Extract features for all splits
            all_paths = train_paths + val_paths + test_paths
            all_features, valid_indices = feature_extractor.extract_and_save_features_enhanced(
                video_paths=all_paths,
                video_dir="data/videos/video_formated_trim",
                save_path=config['features_cache_path']
            )

            if all_features is None:
                logger.error("❌ Enhanced feature extraction failed!")
                return None

            # Split features back into train/val/test
            train_features = all_features[:len(train_paths)]
            val_features = all_features[len(train_paths):len(train_paths)+len(val_paths)]
            test_features = all_features[len(train_paths)+len(val_paths):]

        logger.info(f"🎯 Final enhanced dataset - Train: {len(train_features)}, Val: {len(val_features)}, Test: {len(test_features)}")

        # Create enhanced datasets and loaders
        train_dataset = CosmosLatentDatasetEnhanced(train_features, train_params, augment=True)
        val_dataset = CosmosLatentDatasetEnhanced(val_features, val_params, augment=False)
        test_dataset = CosmosLatentDatasetEnhanced(test_features, test_params, augment=False)

        train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True, num_workers=4, pin_memory=True)
        val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=4, pin_memory=True)
        test_loader = DataLoader(test_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=4, pin_memory=True)

        # Initialize enhanced model
        feature_dim = train_features.shape[1]
        model = EnhancedCosmosRegressionModel(
            feature_dim=feature_dim,
            num_gait_params=len(param_names),
            hidden_dims=[2048, 1024, 512, 256],
            use_attention=config['use_attention'],
            dropout_rate=0.3,
            use_batch_norm=True
        )

        # Initialize enhanced trainer
        trainer = EnhancedCosmosTrainer(
            model=model,
            device=config['device'],
            learning_rate=config['learning_rate'],
            weight_decay=config['weight_decay'],
            gradient_accumulation_steps=config['gradient_accumulation_steps'],
            use_cosine_annealing=config['use_cosine_annealing'],
            use_robust_scaler=config['use_robust_scaler'],
            warmup_epochs=config['warmup_epochs']
        )

        # Train the enhanced model
        logger.info("🚀 Starting enhanced training...")
        history = trainer.fit(
            train_loader=train_loader,
            val_loader=val_loader,
            epochs=config['epochs'],
            save_path="cosmos_enhanced_best_model.pth"
        )

        logger.info("✅ Enhanced patient-aware Cosmos training completed successfully!")

        return {
            'model': model,
            'trainer': trainer,
            'test_loader': test_loader,
            'param_names': param_names,
            'history': history,
            'config': config
        }

    except Exception as e:
        logger.error(f"❌ Error in enhanced main: {e}")
        import traceback
        traceback.print_exc()
        return None


def evaluate_enhanced_model(model, trainer, test_loader, param_names):
    """Enhanced evaluation with comprehensive metrics and visualizations."""
    logger.info("🔍 Starting enhanced patient-aware Cosmos model evaluation...")

    model.eval()
    all_predictions = []
    all_targets = []

    with torch.no_grad():
        for features, targets in tqdm(test_loader, desc="Enhanced Testing"):
            try:
                features = features.to(trainer.device)
                targets = targets.to(trainer.device)

                predictions = model(features)

                # Inverse transform predictions
                pred_np = trainer.scaler.inverse_transform(predictions.cpu().numpy())
                target_np = targets.cpu().numpy()

                all_predictions.append(pred_np)
                all_targets.append(target_np)

            except Exception as e:
                logger.error(f"Error in enhanced test batch: {e}")
                continue

    if not all_predictions:
        logger.error("No successful predictions made")
        return None

    predictions = np.concatenate(all_predictions, axis=0)
    targets = np.concatenate(all_targets, axis=0)

    # Calculate comprehensive per-parameter metrics
    metrics_df = []
    for i, param_name in enumerate(param_names):
        pred_param = predictions[:, i]
        true_param = targets[:, i]

        mae = mean_absolute_error(true_param, pred_param)
        rmse = np.sqrt(mean_squared_error(true_param, pred_param))
        r2 = r2_score(true_param, pred_param)

        # Additional metrics
        mape = np.mean(np.abs((true_param - pred_param) / (true_param + 1e-8))) * 100
        correlation = np.corrcoef(true_param, pred_param)[0, 1]

        metrics_df.append({
            'Parameter': param_name,
            'MAE': mae,
            'RMSE': rmse,
            'R²': r2,
            'MAPE': mape,
            'Correlation': correlation
        })

    metrics_df = pd.DataFrame(metrics_df)

    # Save enhanced metrics
    metrics_df.to_csv("cosmos_enhanced_evaluation_metrics.csv", index=False)
    logger.info("💾 Saved enhanced detailed metrics to cosmos_enhanced_evaluation_metrics.csv")

    # Print enhanced summary
    logger.info("=" * 100)
    logger.info("🚀 ENHANCED PATIENT-AWARE COSMOS EVALUATION RESULTS 🚀")
    logger.info("=" * 100)

    # Sort by R² score for better readability
    metrics_sorted = metrics_df.sort_values('R²', ascending=False)

    logger.info("📊 Per-parameter evaluation results (sorted by R² score):")
    logger.info("-" * 100)

    # Show top 10 best performing parameters
    logger.info("🏆 TOP 10 BEST PERFORMING PARAMETERS:")
    for i, (_, row) in enumerate(metrics_sorted.head(10).iterrows()):
        logger.info(f"{i+1:2d}. {row['Parameter']:20s}: "
                   f"MAE={row['MAE']:8.4f}, RMSE={row['RMSE']:8.4f}, "
                   f"R²={row['R²']:7.4f}, MAPE={row['MAPE']:6.2f}%, "
                   f"Corr={row['Correlation']:6.4f}")

    logger.info("-" * 100)

    # Show bottom 5 parameters
    logger.info("⚠️  BOTTOM 5 PARAMETERS (need improvement):")
    for i, (_, row) in enumerate(metrics_sorted.tail(5).iterrows()):
        logger.info(f"{i+1:2d}. {row['Parameter']:20s}: "
                   f"MAE={row['MAE']:8.4f}, RMSE={row['RMSE']:8.4f}, "
                   f"R²={row['R²']:7.4f}, MAPE={row['MAPE']:6.2f}%, "
                   f"Corr={row['Correlation']:6.4f}")

    # Overall metrics
    overall_mae = mean_absolute_error(targets, predictions)
    overall_rmse = np.sqrt(mean_squared_error(targets, predictions))
    overall_r2 = r2_score(targets, predictions)
    overall_mape = np.mean(np.abs((targets - predictions) / (targets + 1e-8))) * 100

    logger.info("-" * 100)
    logger.info(f"📊 OVERALL ENHANCED PERFORMANCE:")
    logger.info(f"   Overall MAE:  {overall_mae:8.4f}")
    logger.info(f"   Overall RMSE: {overall_rmse:8.4f}")
    logger.info(f"   Overall R²:   {overall_r2:7.4f}")
    logger.info(f"   Overall MAPE: {overall_mape:6.2f}%")

    # Enhanced performance summary
    positive_r2_count = (metrics_df['R²'] > 0).sum()
    good_r2_count = (metrics_df['R²'] > 0.1).sum()
    excellent_r2_count = (metrics_df['R²'] > 0.3).sum()
    outstanding_r2_count = (metrics_df['R²'] > 0.5).sum()

    logger.info("-" * 100)
    logger.info(f"📈 ENHANCED PERFORMANCE SUMMARY:")
    logger.info(f"   Parameters with positive R²: {positive_r2_count}/{len(param_names)} ({positive_r2_count/len(param_names)*100:.1f}%)")
    logger.info(f"   Parameters with R² > 0.1:    {good_r2_count}/{len(param_names)} ({good_r2_count/len(param_names)*100:.1f}%)")
    logger.info(f"   Parameters with R² > 0.3:    {excellent_r2_count}/{len(param_names)} ({excellent_r2_count/len(param_names)*100:.1f}%)")
    logger.info(f"   Parameters with R² > 0.5:    {outstanding_r2_count}/{len(param_names)} ({outstanding_r2_count/len(param_names)*100:.1f}%)")

    logger.info("=" * 100)

    return metrics_df, predictions, targets


if __name__ == "__main__":
    # Run enhanced patient-aware training
    result = main_enhanced()
    if result is not None:
        logger.info("✅ Enhanced patient-aware Cosmos feature extraction and training completed successfully!")

        # Load best enhanced model for evaluation
        checkpoint = torch.load("cosmos_enhanced_best_model.pth", weights_only=False)
        model = result['model']
        model.load_state_dict(checkpoint['model_state_dict'])
        model.to(result['config']['device'])

        trainer = result['trainer']
        trainer.scaler = checkpoint['scaler']

        # Evaluate enhanced model
        metrics_df, predictions, targets = evaluate_enhanced_model(
            model, trainer, result['test_loader'], result['param_names']
        )

        logger.info("🎉 Enhanced patient-aware Cosmos-based gait regression completed successfully!")
        logger.info("✅ No data leakage - patients are properly separated between train/val/test sets!")
        logger.info("🚀 Enhanced model with full dataset, attention, advanced optimization, and multi-scale features!")
    else:
        logger.error("❌ Enhanced patient-aware Cosmos training failed!")
