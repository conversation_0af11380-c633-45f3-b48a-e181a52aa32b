#!/usr/bin/env python3
"""
Final evaluation of Cosmos-based gait regression model
"""

import torch
import pickle
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from torch.utils.data import DataLoader
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import from our main script
from cosmos_gait_regression import CosmosRegressionModel, CosmosLatentDataset, CosmosTrainer

def main():
    """Final evaluation of Cosmos model."""
    logger.info("Starting final Cosmos model evaluation...")
    
    # Load cached features
    with open('cosmos_features_cache.pkl', 'rb') as f:
        cache_data = pickle.load(f)
    
    features = cache_data['features']
    logger.info(f'Loaded {len(features)} cached features with shape {features.shape}')
    
    # Load dataset to get gait parameters and names
    df = pd.read_excel('data/gaitrite_full_dataset.xlsx')
    velocity_idx = df.columns.get_loc('Velocity')
    param_names = df.columns[velocity_idx:].tolist()
    
    # Get valid gait parameters (matching the cached features)
    valid_indices = cache_data['valid_indices']
    valid_df = df.iloc[valid_indices]
    gait_params = valid_df[param_names].values.astype(np.float32)
    
    # Remove samples with missing values (same as in training)
    missing_mask = np.isnan(gait_params).any(axis=1)
    if missing_mask.any():
        features = features[~missing_mask]
        gait_params = gait_params[~missing_mask]
    
    logger.info(f'Final evaluation dataset: {len(features)} samples with {len(param_names)} parameters')
    
    # Create same data splits as training
    train_val_features, test_features, train_val_params, test_params = train_test_split(
        features, gait_params, test_size=0.2, random_state=42
    )
    
    logger.info(f'Test set: {len(test_features)} samples')
    
    # Load best model
    checkpoint = torch.load('cosmos_best_model.pth', weights_only=False)
    model = CosmosRegressionModel(
        feature_dim=features.shape[1],
        num_gait_params=len(param_names),
        hidden_dims=[1024, 512, 256]
    )
    model.load_state_dict(checkpoint['model_state_dict'])
    model.to('cuda')
    model.eval()
    
    # Create trainer for scaler
    trainer = CosmosTrainer(model, device='cuda')
    trainer.scaler = checkpoint['scaler']
    
    logger.info(f'Best validation metrics: {checkpoint["metrics"]}')
    
    # Create test dataset and loader
    test_dataset = CosmosLatentDataset(test_features, test_params)
    test_loader = DataLoader(test_dataset, batch_size=16, shuffle=False, num_workers=4)
    
    # Evaluate on test set
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for features_batch, targets_batch in test_loader:
            features_batch = features_batch.to('cuda')
            targets_batch = targets_batch.to('cuda')
            
            predictions = model(features_batch)
            
            # Inverse transform predictions
            pred_np = trainer.scaler.inverse_transform(predictions.cpu().numpy())
            target_np = targets_batch.cpu().numpy()
            
            all_predictions.append(pred_np)
            all_targets.append(target_np)
    
    predictions = np.concatenate(all_predictions, axis=0)
    targets = np.concatenate(all_targets, axis=0)
    
    logger.info(f'Evaluation completed: {len(predictions)} test samples')
    
    # Calculate per-parameter metrics
    metrics_results = []
    for i, param_name in enumerate(param_names):
        pred_param = predictions[:, i]
        true_param = targets[:, i]
        
        mae = mean_absolute_error(true_param, pred_param)
        rmse = np.sqrt(mean_squared_error(true_param, pred_param))
        r2 = r2_score(true_param, pred_param)
        
        metrics_results.append({
            'Parameter': param_name,
            'MAE': mae,
            'RMSE': rmse,
            'R²': r2
        })
    
    metrics_df = pd.DataFrame(metrics_results)
    
    # Save detailed metrics
    metrics_df.to_csv("cosmos_final_evaluation_metrics.csv", index=False)
    logger.info("Saved detailed metrics to cosmos_final_evaluation_metrics.csv")
    
    # Print results
    logger.info("=" * 80)
    logger.info("🚀 COSMOS-BASED FINAL EVALUATION RESULTS 🚀")
    logger.info("=" * 80)
    
    # Sort by R² score for better readability
    metrics_sorted = metrics_df.sort_values('R²', ascending=False)
    
    logger.info("Per-parameter evaluation results (sorted by R² score):")
    logger.info("-" * 80)
    
    # Show top 10 best performing parameters
    logger.info("🏆 TOP 10 BEST PERFORMING PARAMETERS:")
    for i, (_, row) in enumerate(metrics_sorted.head(10).iterrows()):
        logger.info(f"{i+1:2d}. {row['Parameter']:20s}: MAE={row['MAE']:8.4f}, RMSE={row['RMSE']:8.4f}, R²={row['R²']:7.4f}")
    
    logger.info("-" * 80)
    
    # Show bottom 5 parameters
    logger.info("⚠️  BOTTOM 5 PARAMETERS (need improvement):")
    for i, (_, row) in enumerate(metrics_sorted.tail(5).iterrows()):
        logger.info(f"{i+1:2d}. {row['Parameter']:20s}: MAE={row['MAE']:8.4f}, RMSE={row['RMSE']:8.4f}, R²={row['R²']:7.4f}")
    
    # Overall metrics
    overall_mae = mean_absolute_error(targets, predictions)
    overall_rmse = np.sqrt(mean_squared_error(targets, predictions))
    overall_r2 = r2_score(targets, predictions)
    
    logger.info("-" * 80)
    logger.info(f"📊 OVERALL PERFORMANCE:")
    logger.info(f"   Overall MAE:  {overall_mae:8.4f}")
    logger.info(f"   Overall RMSE: {overall_rmse:8.4f}")
    logger.info(f"   Overall R²:   {overall_r2:7.4f}")
    
    # Performance summary
    positive_r2_count = (metrics_df['R²'] > 0).sum()
    good_r2_count = (metrics_df['R²'] > 0.1).sum()
    excellent_r2_count = (metrics_df['R²'] > 0.3).sum()
    
    logger.info("-" * 80)
    logger.info(f"📈 PERFORMANCE SUMMARY:")
    logger.info(f"   Parameters with positive R²: {positive_r2_count}/{len(param_names)} ({positive_r2_count/len(param_names)*100:.1f}%)")
    logger.info(f"   Parameters with R² > 0.1:    {good_r2_count}/{len(param_names)} ({good_r2_count/len(param_names)*100:.1f}%)")
    logger.info(f"   Parameters with R² > 0.3:    {excellent_r2_count}/{len(param_names)} ({excellent_r2_count/len(param_names)*100:.1f}%)")
    
    logger.info("=" * 80)
    
    # Create visualization
    plt.figure(figsize=(15, 10))
    
    # Plot 1: R² scores by parameter
    plt.subplot(2, 2, 1)
    metrics_sorted_plot = metrics_sorted.head(15)  # Top 15 for readability
    plt.barh(range(len(metrics_sorted_plot)), metrics_sorted_plot['R²'])
    plt.yticks(range(len(metrics_sorted_plot)), metrics_sorted_plot['Parameter'])
    plt.xlabel('R² Score')
    plt.title('Top 15 Parameters by R² Score')
    plt.grid(True, alpha=0.3)
    
    # Plot 2: MAE by parameter
    plt.subplot(2, 2, 2)
    mae_sorted = metrics_df.sort_values('MAE').head(15)
    plt.barh(range(len(mae_sorted)), mae_sorted['MAE'])
    plt.yticks(range(len(mae_sorted)), mae_sorted['Parameter'])
    plt.xlabel('MAE')
    plt.title('Top 15 Parameters by MAE (Lower is Better)')
    plt.grid(True, alpha=0.3)
    
    # Plot 3: Overall performance distribution
    plt.subplot(2, 2, 3)
    plt.hist(metrics_df['R²'], bins=20, alpha=0.7, edgecolor='black')
    plt.xlabel('R² Score')
    plt.ylabel('Number of Parameters')
    plt.title('Distribution of R² Scores')
    plt.axvline(overall_r2, color='red', linestyle='--', label=f'Overall R²: {overall_r2:.3f}')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot 4: Prediction vs Ground Truth for best parameter
    plt.subplot(2, 2, 4)
    best_param_idx = metrics_sorted.index[0]
    best_param_name = metrics_sorted.iloc[0]['Parameter']
    best_param_r2 = metrics_sorted.iloc[0]['R²']
    
    param_idx = param_names.index(best_param_name)
    plt.scatter(targets[:, param_idx], predictions[:, param_idx], alpha=0.6)
    plt.plot([targets[:, param_idx].min(), targets[:, param_idx].max()], 
             [targets[:, param_idx].min(), targets[:, param_idx].max()], 'r--', lw=2)
    plt.xlabel('Ground Truth')
    plt.ylabel('Prediction')
    plt.title(f'Best Parameter: {best_param_name} (R²={best_param_r2:.3f})')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('cosmos_final_evaluation_results.png', dpi=300, bbox_inches='tight')
    logger.info("Saved visualization to cosmos_final_evaluation_results.png")
    
    return metrics_df, predictions, targets

if __name__ == "__main__":
    metrics_df, predictions, targets = main()
    print("\n🎉 Cosmos-based gait regression evaluation completed successfully!")
