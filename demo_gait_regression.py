#!/usr/bin/env python3
"""
Demo script for Gait Parameter Regression Model

This script demonstrates the basic functionality of the gait regression model
with a small subset of data for quick testing and validation.
"""

import os
import sys
import logging
import numpy as np
import torch
import matplotlib.pyplot as plt
from typing import List, Tuple
import warnings
warnings.filterwarnings('ignore')

# Import our modules
from gait_regression_model import (
    load_and_preprocess_data, create_data_loaders,
    VideoFeatureExtractor, GaitRegressionModel, GaitRegressionTrainer,
    plot_training_history, evaluate_model
)

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def check_prerequisites() -> bool:
    """Check if all prerequisites are available."""
    logger.info("Checking prerequisites...")
    
    # Check CUDA availability
    if torch.cuda.is_available():
        logger.info(f"CUDA available: {torch.cuda.get_device_name()}")
        device = "cuda"
    else:
        logger.warning("CUDA not available, using CPU")
        device = "cpu"
    
    # Check for Cosmos tokenizer checkpoints
    model_name = "Cosmos-1.0-Tokenizer-CV8x8x8"
    encoder_path = f"pretrained_ckpts/{model_name}/encoder.jit"
    
    if not os.path.exists(encoder_path):
        logger.error(f"Cosmos tokenizer checkpoint not found: {encoder_path}")
        logger.error("Please download the pretrained models first:")
        logger.error("1. Set up Hugging Face token")
        logger.error("2. Run the download script in the README")
        return False
    else:
        logger.info(f"Found Cosmos tokenizer checkpoint: {encoder_path}")
    
    # Check data availability
    data_path = "data/gaitrite_full_dataset.xlsx"
    video_dir = "data/videos/video_formated_trim"
    
    if not os.path.exists(data_path):
        logger.error(f"Dataset not found: {data_path}")
        return False
    else:
        logger.info(f"Found dataset: {data_path}")
    
    if not os.path.exists(video_dir):
        logger.error(f"Video directory not found: {video_dir}")
        return False
    else:
        video_count = len([f for f in os.listdir(video_dir) if f.endswith('.avi')])
        logger.info(f"Found video directory with {video_count} videos: {video_dir}")
    
    return True


def demo_data_loading():
    """Demonstrate data loading functionality."""
    logger.info("=" * 50)
    logger.info("DEMO: Data Loading")
    logger.info("=" * 50)
    
    try:
        # Load and preprocess data
        video_paths, gait_params, param_names = load_and_preprocess_data()
        
        logger.info(f"Successfully loaded {len(video_paths)} videos")
        logger.info(f"Gait parameters shape: {gait_params.shape}")
        logger.info(f"Parameter names: {param_names}")
        
        # Show basic statistics
        import pandas as pd
        df_stats = pd.DataFrame(gait_params, columns=param_names)
        logger.info("Gait parameter statistics:")
        logger.info(f"\n{df_stats.describe()}")
        
        return video_paths, gait_params, param_names
        
    except Exception as e:
        logger.error(f"Error in data loading demo: {e}")
        return None, None, None


def demo_feature_extraction(video_paths: List[str], sample_size: int = 2):
    """Demonstrate feature extraction with a small sample."""
    logger.info("=" * 50)
    logger.info("DEMO: Feature Extraction")
    logger.info("=" * 50)
    
    try:
        # Initialize feature extractor
        device = "cuda" if torch.cuda.is_available() else "cpu"
        feature_extractor = VideoFeatureExtractor(
            model_name="Cosmos-1.0-Tokenizer-CV8x8x8",
            device=device,
            dtype="bfloat16" if device == "cuda" else "float32",
            temporal_window=17  # Smaller window for demo
        )
        
        logger.info(f"Initialized feature extractor on {device}")
        
        # Test feature extraction on a few samples
        sample_videos = video_paths[:sample_size]
        logger.info(f"Testing feature extraction on {len(sample_videos)} videos")
        
        for i, video_path in enumerate(sample_videos):
            try:
                from cosmos_tokenizer.utils import read_video
                full_path = os.path.join("data/videos/video_formated_trim", video_path)
                
                if os.path.exists(full_path):
                    # Load video
                    video = read_video(full_path)
                    logger.info(f"Video {i+1}: {video_path}, shape: {video.shape}")
                    
                    # Extract features
                    features = feature_extractor.extract_features(video)
                    logger.info(f"Features shape: {features.shape}")
                    
                    # Show feature statistics
                    logger.info(f"Feature stats - Mean: {features.mean():.4f}, "
                               f"Std: {features.std():.4f}, "
                               f"Min: {features.min():.4f}, "
                               f"Max: {features.max():.4f}")
                else:
                    logger.warning(f"Video file not found: {full_path}")
                    
            except Exception as e:
                logger.error(f"Error processing video {video_path}: {e}")
        
        return feature_extractor
        
    except Exception as e:
        logger.error(f"Error in feature extraction demo: {e}")
        return None


def demo_model_architecture():
    """Demonstrate model architecture and forward pass."""
    logger.info("=" * 50)
    logger.info("DEMO: Model Architecture")
    logger.info("=" * 50)
    
    try:
        # Create model
        model = GaitRegressionModel(
            input_channels=16,
            num_gait_params=6,
            hidden_dims=[256, 128, 64]  # Smaller for demo
        )
        
        # Count parameters
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        logger.info(f"Model created successfully")
        logger.info(f"Total parameters: {total_params:,}")
        logger.info(f"Trainable parameters: {trainable_params:,}")
        
        # Test forward pass with dummy data
        batch_size = 2
        dummy_features = torch.randn(batch_size, 16, 4, 8, 8)
        
        model.eval()
        with torch.no_grad():
            output = model(dummy_features)
        
        logger.info(f"Forward pass successful")
        logger.info(f"Input shape: {dummy_features.shape}")
        logger.info(f"Output shape: {output.shape}")
        logger.info(f"Output sample: {output[0].numpy()}")
        
        return model
        
    except Exception as e:
        logger.error(f"Error in model architecture demo: {e}")
        return None


def demo_mini_training(video_paths: List[str], gait_params: np.ndarray, param_names: List[str]):
    """Demonstrate training with a very small dataset."""
    logger.info("=" * 50)
    logger.info("DEMO: Mini Training")
    logger.info("=" * 50)
    
    try:
        # Use only a small subset for quick demo
        subset_size = min(20, len(video_paths))
        demo_video_paths = video_paths[:subset_size]
        demo_gait_params = gait_params[:subset_size]
        
        logger.info(f"Using subset of {subset_size} samples for demo training")
        
        # Create data loaders with small batch size
        train_loader, val_loader, test_loader = create_data_loaders(
            demo_video_paths, demo_gait_params,
            test_size=0.3,
            val_size=0.2,
            batch_size=1,  # Very small batch for demo
            random_state=42
        )
        
        logger.info(f"Created data loaders - Train: {len(train_loader.dataset)}, "
                   f"Val: {len(val_loader.dataset)}, Test: {len(test_loader.dataset)}")
        
        # Initialize models
        device = "cuda" if torch.cuda.is_available() else "cpu"
        
        feature_extractor = VideoFeatureExtractor(
            model_name="Cosmos-1.0-Tokenizer-CV8x8x8",
            device=device,
            dtype="bfloat16" if device == "cuda" else "float32",
            temporal_window=17
        )
        
        regression_model = GaitRegressionModel(
            input_channels=16,
            num_gait_params=len(param_names),
            hidden_dims=[128, 64]  # Smaller for demo
        )
        
        # Initialize trainer
        trainer = GaitRegressionTrainer(
            model=regression_model,
            feature_extractor=feature_extractor,
            device=device,
            learning_rate=1e-3,  # Higher learning rate for demo
            weight_decay=1e-4
        )
        
        # Train for just a few epochs
        logger.info("Starting mini training (3 epochs)...")
        history = trainer.fit(
            train_loader=train_loader,
            val_loader=val_loader,
            epochs=3,
            save_path="demo_model.pth"
        )
        
        # Plot training history
        plot_training_history(history, "demo_training_history.png")
        logger.info("Training history plot saved as demo_training_history.png")
        
        # Quick evaluation
        if len(test_loader.dataset) > 0:
            logger.info("Running quick evaluation...")
            metrics = evaluate_model(
                model=regression_model,
                feature_extractor=feature_extractor,
                test_loader=test_loader,
                param_names=param_names,
                device=device,
                save_path="demo_evaluation.png"
            )
            
            logger.info("Evaluation completed - check demo_evaluation.png")
        
        return history, metrics if len(test_loader.dataset) > 0 else None
        
    except Exception as e:
        logger.error(f"Error in mini training demo: {e}")
        import traceback
        traceback.print_exc()
        return None, None


def main():
    """Main demo function."""
    logger.info("Starting Gait Regression Model Demo")
    logger.info("This demo will test basic functionality with a small dataset subset")
    
    # Check prerequisites
    if not check_prerequisites():
        logger.error("Prerequisites not met. Please check the setup.")
        return
    
    # Demo 1: Data Loading
    video_paths, gait_params, param_names = demo_data_loading()
    if video_paths is None:
        logger.error("Data loading failed. Cannot continue demo.")
        return
    
    # Demo 2: Feature Extraction
    feature_extractor = demo_feature_extraction(video_paths, sample_size=2)
    if feature_extractor is None:
        logger.warning("Feature extraction demo failed, but continuing...")
    
    # Demo 3: Model Architecture
    model = demo_model_architecture()
    if model is None:
        logger.error("Model architecture demo failed. Cannot continue.")
        return
    
    # Demo 4: Mini Training (optional - can be skipped if too slow)
    logger.info("Would you like to run the mini training demo? (This may take several minutes)")
    logger.info("You can skip this by pressing Ctrl+C and the demo will complete successfully")
    
    try:
        history, metrics = demo_mini_training(video_paths, gait_params, param_names)
        if history is not None:
            logger.info("Mini training demo completed successfully!")
        else:
            logger.warning("Mini training demo had issues, but basic functionality is working")
    except KeyboardInterrupt:
        logger.info("Mini training demo skipped by user")
    except Exception as e:
        logger.warning(f"Mini training demo failed: {e}")
        logger.info("But basic functionality is working!")
    
    logger.info("=" * 50)
    logger.info("DEMO COMPLETED SUCCESSFULLY!")
    logger.info("=" * 50)
    logger.info("Key takeaways:")
    logger.info("✓ Data loading and preprocessing works")
    logger.info("✓ Cosmos tokenizer integration works")
    logger.info("✓ Model architecture is functional")
    logger.info("✓ Training pipeline is ready")
    logger.info("")
    logger.info("To run full training, use: python gait_regression_model.py")
    logger.info("To run tests, use: python test_gait_regression.py")


if __name__ == "__main__":
    main()
