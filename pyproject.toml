[project]
name = "cosmos-tokenizer"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10.16"
dependencies = [
    "asttokens==3.0.0",
    "certifi==2025.8.3",
    "charset-normalizer==3.4.2",
    "contourpy==1.3.2",
    "cycler==0.12.1",
    "decorator==5.2.1",
    "einops==0.7.0",
    "einx==0.1.3",
    "exceptiongroup==1.3.0",
    "executing==2.2.0",
    "filelock==3.18.0",
    "fonttools==4.59.0",
    "frozendict==2.4.6",
    "fsspec==2025.7.0",
    "hf-xet==1.1.7",
    "huggingface-hub==0.34.3",
    "idna==3.10",
    "ipdb==0.13.13",
    "ipython==8.37.0",
    "jedi==0.19.2",
    "jinja2==3.1.6",
    "kiwisolver==1.4.8",
    "loguru==0.7.3",
    "markupsafe==3.0.2",
    "matplotlib==3.10.5",
    "matplotlib-inline==0.1.7",
    "mediapy==1.1.6",
    "mpmath==1.3.0",
    "networkx==3.4.2",
    "numpy==2.2.6",
    "nvidia-cublas-cu12==********",
    "nvidia-cuda-cupti-cu12==12.8.90",
    "nvidia-cuda-nvrtc-cu12==12.8.93",
    "nvidia-cuda-runtime-cu12==12.8.90",
    "nvidia-cudnn-cu12==*********",
    "nvidia-cufft-cu12==*********",
    "nvidia-cufile-cu12==********",
    "nvidia-curand-cu12==*********",
    "nvidia-cusolver-cu12==*********",
    "nvidia-cusparse-cu12==*********",
    "nvidia-cusparselt-cu12==0.7.1",
    "nvidia-nccl-cu12==2.27.3",
    "nvidia-nvjitlink-cu12==12.8.93",
    "nvidia-nvtx-cu12==12.8.90",
    "opencv-python==*********",
    "openpyxl>=3.1.5",
    "packaging==25.0",
    "pandas>=2.3.1",
    "parso==0.8.4",
    "pexpect==4.9.0",
    "pillow==11.3.0",
    "prompt-toolkit==3.0.51",
    "ptyprocess==0.7.0",
    "pure-eval==0.2.3",
    "pygments==2.19.2",
    "pyparsing==3.2.3",
    "python-dateutil==2.9.0.post0",
    "pyyaml==6.0.2",
    "requests==2.32.4",
    "setuptools==80.9.0",
    "six==1.17.0",
    "stack-data==0.6.3",
    "sympy==1.14.0",
    "tomli==2.2.1",
    "torch==2.8.0",
    "tqdm==4.67.1",
    "traitlets==5.14.3",
    "triton==3.4.0",
    "typing-extensions==4.14.1",
    "urllib3==2.5.0",
    "wcwidth==0.2.13",
]
