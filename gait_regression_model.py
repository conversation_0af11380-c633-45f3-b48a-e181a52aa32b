#!/usr/bin/env python3
"""
Gait Parameter Regression Model using Cosmos Video Tokenizer

This module implements a regression model that predicts gait parameters from videos
using the Cosmos video tokenizer as a feature extractor.
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# Import Cosmos tokenizer components
from cosmos_tokenizer.video_lib import CausalVideoTokenizer
from cosmos_tokenizer.utils import read_video, pad_video_batch, numpy2tensor, tensor2numpy

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class GaitVideoDataset(Dataset):
    """Dataset class for gait videos and parameters."""
    
    def __init__(self, 
                 video_paths: List[str], 
                 gait_params: np.ndarray,
                 video_dir: str = "data/videos/video_formated_trim",
                 transform=None):
        """
        Initialize the dataset.
        
        Args:
            video_paths: List of video file paths
            gait_params: Array of gait parameters (N, num_params)
            video_dir: Directory containing video files
            transform: Optional transform to apply to videos
        """
        self.video_paths = video_paths
        self.gait_params = gait_params
        self.video_dir = video_dir
        self.transform = transform
        
        # Validate that we have matching data
        assert len(video_paths) == len(gait_params), \
            f"Mismatch: {len(video_paths)} videos vs {len(gait_params)} parameter sets"
    
    def __len__(self):
        return len(self.video_paths)
    
    def __getitem__(self, idx):
        """Get a single video and its corresponding gait parameters."""
        video_path = os.path.join(self.video_dir, self.video_paths[idx])
        
        # Load video
        try:
            video = read_video(video_path)  # Returns T x H x W x 3, range [0,255]
            if self.transform:
                video = self.transform(video)
        except Exception as e:
            logger.error(f"Error loading video {video_path}: {e}")
            # Return a dummy video if loading fails
            video = np.zeros((16, 224, 224, 3), dtype=np.uint8)
        
        # Get gait parameters
        params = self.gait_params[idx].astype(np.float32)
        
        return video, params


class VideoFeatureExtractor(nn.Module):
    """Feature extractor using Cosmos video tokenizer."""
    
    def __init__(self, 
                 model_name: str = "Cosmos-1.0-Tokenizer-CV8x8x8",
                 device: str = "cuda",
                 dtype: str = "bfloat16",
                 temporal_window: int = 49):
        """
        Initialize the feature extractor.
        
        Args:
            model_name: Name of the Cosmos tokenizer model
            device: Device to run on
            dtype: Data type for computation
            temporal_window: Temporal window size for processing
        """
        super().__init__()
        self.model_name = model_name
        self.device = device
        self.dtype = dtype
        self.temporal_window = temporal_window
        
        # Initialize Cosmos tokenizer
        encoder_ckpt = f"pretrained_ckpts/{model_name}/encoder.jit"
        
        if not os.path.exists(encoder_ckpt):
            raise FileNotFoundError(f"Encoder checkpoint not found: {encoder_ckpt}")
        
        self.tokenizer = CausalVideoTokenizer(
            checkpoint_enc=encoder_ckpt,
            device=device,
            dtype=dtype,
        )
        
        logger.info(f"Initialized {model_name} feature extractor")
    
    def extract_features(self, video: np.ndarray) -> torch.Tensor:
        """
        Extract features from a video using Cosmos tokenizer.
        
        Args:
            video: Video array of shape (T, H, W, 3) in range [0, 255]
            
        Returns:
            Extracted features as tensor
        """
        # Add batch dimension: (1, T, H, W, 3)
        batched_video = np.expand_dims(video, axis=0)
        
        # Process video in temporal windows
        num_frames = video.shape[0]
        feature_list = []
        
        for idx in range(0, (num_frames - 1) // self.temporal_window + 1):
            start, end = idx * self.temporal_window, (idx + 1) * self.temporal_window
            input_video = batched_video[:, start:end, ...]
            
            # Pad video for processing
            padded_input_video, crop_region = pad_video_batch(input_video)
            
            # Convert to tensor
            input_tensor = numpy2tensor(
                padded_input_video, 
                dtype=getattr(torch, self.dtype), 
                device=self.device
            )
            
            # Extract features using encoder
            with torch.no_grad():
                features = self.tokenizer.encode(input_tensor)[0]  # (B, C, t, h, w)
                feature_list.append(features)
        
        # Concatenate features along temporal dimension
        if len(feature_list) > 1:
            features = torch.cat(feature_list, dim=2)  # Concatenate along time dimension
        else:
            features = feature_list[0]
        
        return features.squeeze(0)  # Remove batch dimension: (C, t, h, w)


class GaitRegressionModel(nn.Module):
    """Regression model for predicting gait parameters from video features."""
    
    def __init__(self, 
                 input_channels: int = 16,
                 num_gait_params: int = 6,
                 hidden_dims: List[int] = [512, 256, 128]):
        """
        Initialize the regression model.
        
        Args:
            input_channels: Number of input channels from feature extractor
            num_gait_params: Number of gait parameters to predict
            hidden_dims: List of hidden layer dimensions
        """
        super().__init__()
        
        # Global average pooling to reduce spatial and temporal dimensions
        self.global_pool = nn.AdaptiveAvgPool3d((1, 1, 1))
        
        # Build fully connected layers
        layers = []
        prev_dim = input_channels
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.BatchNorm1d(hidden_dim)
            ])
            prev_dim = hidden_dim
        
        # Output layer
        layers.append(nn.Linear(prev_dim, num_gait_params))
        
        self.regressor = nn.Sequential(*layers)
        
        logger.info(f"Initialized regression model with {sum(p.numel() for p in self.parameters())} parameters")
    
    def forward(self, features: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of the regression model.
        
        Args:
            features: Input features of shape (B, C, t, h, w)
            
        Returns:
            Predicted gait parameters of shape (B, num_gait_params)
        """
        # Global pooling: (B, C, t, h, w) -> (B, C, 1, 1, 1)
        pooled = self.global_pool(features)
        
        # Flatten: (B, C, 1, 1, 1) -> (B, C)
        flattened = pooled.view(pooled.size(0), -1)
        
        # Regression: (B, C) -> (B, num_gait_params)
        output = self.regressor(flattened)
        
        return output


def load_and_preprocess_data(data_path: str = "data/gaitrite_full_dataset.xlsx",
                           video_dir: str = "data/videos/video_formated_trim") -> Tuple[List[str], np.ndarray, List[str]]:
    """
    Load and preprocess the gait dataset.
    
    Args:
        data_path: Path to the Excel file containing gait data
        video_dir: Directory containing video files
        
    Returns:
        Tuple of (video_paths, gait_parameters, parameter_names)
    """
    logger.info("Loading gait dataset...")
    
    # Load the dataset
    df = pd.read_excel(data_path)
    logger.info(f"Loaded dataset with {len(df)} samples and {len(df.columns)} columns")
    
    # Define target gait parameters
    target_params = [
        'Velocity', 'Cadence', 'Stride_Len_L', 'Stride_Len_R', 
        'Swing_Perc_L', 'Swing_Perc_R'
    ]
    
    # Extract video file names from VideoFile column
    video_paths = []
    valid_indices = []
    
    for idx, row in df.iterrows():
        video_file = row['VideoFile']
        if pd.isna(video_file):
            continue
            
        # Extract filename from full path
        filename = os.path.basename(video_file)
        video_path = os.path.join(video_dir, filename)
        
        # Check if video file exists
        if os.path.exists(video_path):
            video_paths.append(filename)
            valid_indices.append(idx)
        else:
            logger.warning(f"Video file not found: {video_path}")
    
    logger.info(f"Found {len(video_paths)} valid video files out of {len(df)} samples")
    
    # Extract gait parameters for valid samples
    valid_df = df.iloc[valid_indices]
    gait_params = valid_df[target_params].values.astype(np.float32)
    
    # Check for missing values
    missing_mask = np.isnan(gait_params).any(axis=1)
    if missing_mask.any():
        logger.warning(f"Removing {missing_mask.sum()} samples with missing gait parameters")
        video_paths = [video_paths[i] for i in range(len(video_paths)) if not missing_mask[i]]
        gait_params = gait_params[~missing_mask]
    
    logger.info(f"Final dataset: {len(video_paths)} samples with {len(target_params)} gait parameters")
    logger.info(f"Gait parameter statistics:\n{pd.DataFrame(gait_params, columns=target_params).describe()}")
    
    return video_paths, gait_params, target_params


class GaitRegressionTrainer:
    """Trainer class for the gait regression model."""

    def __init__(self,
                 model: nn.Module,
                 feature_extractor: VideoFeatureExtractor,
                 device: str = "cuda",
                 learning_rate: float = 1e-4,
                 weight_decay: float = 1e-5):
        """
        Initialize the trainer.

        Args:
            model: The regression model
            feature_extractor: The video feature extractor
            device: Device to train on
            learning_rate: Learning rate for optimizer
            weight_decay: Weight decay for regularization
        """
        self.model = model.to(device)
        self.feature_extractor = feature_extractor
        self.device = device

        # Initialize optimizer and loss function
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay
        )
        self.criterion = nn.MSELoss()
        self.scaler = StandardScaler()

        # Training history
        self.train_losses = []
        self.val_losses = []
        self.best_val_loss = float('inf')

        logger.info("Initialized trainer")

    def extract_features_batch(self, videos: List[np.ndarray]) -> torch.Tensor:
        """Extract features from a batch of videos."""
        features_list = []

        for video in videos:
            features = self.feature_extractor.extract_features(video)
            features_list.append(features.unsqueeze(0))  # Add batch dimension

        # Stack all features: (B, C, t, h, w)
        batch_features = torch.cat(features_list, dim=0)
        return batch_features

    def train_epoch(self, dataloader: DataLoader) -> float:
        """Train for one epoch."""
        self.model.train()
        total_loss = 0.0
        num_batches = 0

        for videos, targets in tqdm(dataloader, desc="Training"):
            # Extract features from videos
            with torch.no_grad():
                features = self.extract_features_batch(videos)

            # Move targets to device
            targets = targets.to(self.device)

            # Forward pass
            self.optimizer.zero_grad()
            predictions = self.model(features)
            loss = self.criterion(predictions, targets)

            # Backward pass
            loss.backward()
            self.optimizer.step()

            total_loss += loss.item()
            num_batches += 1

        avg_loss = total_loss / num_batches
        self.train_losses.append(avg_loss)
        return avg_loss

    def validate(self, dataloader: DataLoader) -> Tuple[float, Dict[str, float]]:
        """Validate the model."""
        self.model.eval()
        total_loss = 0.0
        all_predictions = []
        all_targets = []
        num_batches = 0

        with torch.no_grad():
            for videos, targets in tqdm(dataloader, desc="Validating"):
                # Extract features from videos
                features = self.extract_features_batch(videos)

                # Move targets to device
                targets = targets.to(self.device)

                # Forward pass
                predictions = self.model(features)
                loss = self.criterion(predictions, targets)

                total_loss += loss.item()
                num_batches += 1

                # Store predictions and targets for metrics
                all_predictions.append(predictions.cpu().numpy())
                all_targets.append(targets.cpu().numpy())

        avg_loss = total_loss / num_batches
        self.val_losses.append(avg_loss)

        # Calculate additional metrics
        predictions = np.concatenate(all_predictions, axis=0)
        targets = np.concatenate(all_targets, axis=0)

        metrics = {
            'mse': mean_squared_error(targets, predictions),
            'mae': mean_absolute_error(targets, predictions),
            'r2': r2_score(targets, predictions)
        }

        return avg_loss, metrics

    def fit(self,
            train_loader: DataLoader,
            val_loader: DataLoader,
            epochs: int = 50,
            save_path: str = "best_gait_model.pth") -> Dict:
        """
        Train the model.

        Args:
            train_loader: Training data loader
            val_loader: Validation data loader
            epochs: Number of epochs to train
            save_path: Path to save the best model

        Returns:
            Training history dictionary
        """
        logger.info(f"Starting training for {epochs} epochs...")

        for epoch in range(epochs):
            # Train
            train_loss = self.train_epoch(train_loader)

            # Validate
            val_loss, metrics = self.validate(val_loader)

            # Log progress
            logger.info(f"Epoch {epoch+1}/{epochs}: "
                       f"Train Loss: {train_loss:.4f}, "
                       f"Val Loss: {val_loss:.4f}, "
                       f"Val R²: {metrics['r2']:.4f}")

            # Save best model
            if val_loss < self.best_val_loss:
                self.best_val_loss = val_loss
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'val_loss': val_loss,
                    'metrics': metrics
                }, save_path)
                logger.info(f"Saved best model with validation loss: {val_loss:.4f}")

        return {
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'best_val_loss': self.best_val_loss
        }


def create_data_loaders(video_paths: List[str],
                       gait_params: np.ndarray,
                       test_size: float = 0.2,
                       val_size: float = 0.1,
                       batch_size: int = 4,
                       random_state: int = 42) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """
    Create train, validation, and test data loaders.

    Args:
        video_paths: List of video file paths
        gait_params: Array of gait parameters
        test_size: Fraction of data for testing
        val_size: Fraction of training data for validation
        batch_size: Batch size for data loaders
        random_state: Random state for reproducibility

    Returns:
        Tuple of (train_loader, val_loader, test_loader)
    """
    # First split: separate test set
    train_val_paths, test_paths, train_val_params, test_params = train_test_split(
        video_paths, gait_params, test_size=test_size, random_state=random_state
    )

    # Second split: separate validation from training
    train_paths, val_paths, train_params, val_params = train_test_split(
        train_val_paths, train_val_params, test_size=val_size, random_state=random_state
    )

    logger.info(f"Data split: Train={len(train_paths)}, Val={len(val_paths)}, Test={len(test_paths)}")

    # Create datasets
    train_dataset = GaitVideoDataset(train_paths, train_params)
    val_dataset = GaitVideoDataset(val_paths, val_params)
    test_dataset = GaitVideoDataset(test_paths, test_params)

    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=2)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=2)

    return train_loader, val_loader, test_loader


def plot_training_history(history: Dict, save_path: str = "training_history.png"):
    """Plot training and validation loss curves."""
    plt.figure(figsize=(10, 6))

    plt.subplot(1, 2, 1)
    plt.plot(history['train_losses'], label='Training Loss', color='blue')
    plt.plot(history['val_losses'], label='Validation Loss', color='red')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    plt.grid(True)

    plt.subplot(1, 2, 2)
    plt.plot(history['train_losses'], label='Training Loss', color='blue')
    plt.plot(history['val_losses'], label='Validation Loss', color='red')
    plt.xlabel('Epoch')
    plt.ylabel('Loss (Log Scale)')
    plt.title('Training and Validation Loss (Log Scale)')
    plt.yscale('log')
    plt.legend()
    plt.grid(True)

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    logger.info(f"Training history plot saved to {save_path}")


def evaluate_model(model: nn.Module,
                  feature_extractor: VideoFeatureExtractor,
                  test_loader: DataLoader,
                  param_names: List[str],
                  device: str = "cuda",
                  save_path: str = "evaluation_results.png") -> Dict:
    """
    Evaluate the model on test data and create visualizations.

    Args:
        model: Trained regression model
        feature_extractor: Video feature extractor
        test_loader: Test data loader
        param_names: Names of gait parameters
        device: Device to run evaluation on
        save_path: Path to save evaluation plots

    Returns:
        Dictionary containing evaluation metrics
    """
    model.eval()
    all_predictions = []
    all_targets = []

    logger.info("Evaluating model on test data...")

    with torch.no_grad():
        for videos, targets in tqdm(test_loader, desc="Testing"):
            # Extract features from videos
            features_list = []
            for video in videos:
                features = feature_extractor.extract_features(video)
                features_list.append(features.unsqueeze(0))

            batch_features = torch.cat(features_list, dim=0)

            # Predict
            predictions = model(batch_features)

            all_predictions.append(predictions.cpu().numpy())
            all_targets.append(targets.numpy())

    # Concatenate all results
    predictions = np.concatenate(all_predictions, axis=0)
    targets = np.concatenate(all_targets, axis=0)

    # Calculate metrics for each parameter
    metrics = {}
    for i, param_name in enumerate(param_names):
        pred_param = predictions[:, i]
        true_param = targets[:, i]

        metrics[param_name] = {
            'mse': mean_squared_error(true_param, pred_param),
            'mae': mean_absolute_error(true_param, pred_param),
            'r2': r2_score(true_param, pred_param)
        }

    # Overall metrics
    metrics['overall'] = {
        'mse': mean_squared_error(targets, predictions),
        'mae': mean_absolute_error(targets, predictions),
        'r2': r2_score(targets, predictions)
    }

    # Create evaluation plots
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()

    for i, param_name in enumerate(param_names):
        ax = axes[i]
        pred_param = predictions[:, i]
        true_param = targets[:, i]

        # Scatter plot of predictions vs true values
        ax.scatter(true_param, pred_param, alpha=0.6, s=20)

        # Perfect prediction line
        min_val = min(true_param.min(), pred_param.min())
        max_val = max(true_param.max(), pred_param.max())
        ax.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2, label='Perfect Prediction')

        # Labels and title
        ax.set_xlabel(f'True {param_name}')
        ax.set_ylabel(f'Predicted {param_name}')
        ax.set_title(f'{param_name}\nR² = {metrics[param_name]["r2"]:.3f}, '
                    f'MAE = {metrics[param_name]["mae"]:.3f}')
        ax.legend()
        ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()

    # Print metrics summary
    logger.info("Evaluation Results:")
    logger.info("-" * 50)
    for param_name in param_names:
        logger.info(f"{param_name}:")
        logger.info(f"  R² Score: {metrics[param_name]['r2']:.4f}")
        logger.info(f"  MAE: {metrics[param_name]['mae']:.4f}")
        logger.info(f"  MSE: {metrics[param_name]['mse']:.4f}")

    logger.info(f"\nOverall Performance:")
    logger.info(f"  R² Score: {metrics['overall']['r2']:.4f}")
    logger.info(f"  MAE: {metrics['overall']['mae']:.4f}")
    logger.info(f"  MSE: {metrics['overall']['mse']:.4f}")

    return metrics


def main():
    """Main training and evaluation pipeline."""
    # Configuration
    config = {
        'model_name': 'Cosmos-1.0-Tokenizer-CV8x8x8',
        'device': 'cuda' if torch.cuda.is_available() else 'cpu',
        'dtype': 'bfloat16',
        'temporal_window': 49,
        'batch_size': 2,  # Small batch size due to memory constraints
        'epochs': 30,
        'learning_rate': 1e-4,
        'weight_decay': 1e-5,
        'test_size': 0.2,
        'val_size': 0.1,
        'random_state': 42
    }

    logger.info("Starting Gait Parameter Regression Pipeline")
    logger.info(f"Configuration: {config}")

    # Load and preprocess data
    video_paths, gait_params, param_names = load_and_preprocess_data()

    # Create data loaders
    train_loader, val_loader, test_loader = create_data_loaders(
        video_paths, gait_params,
        test_size=config['test_size'],
        val_size=config['val_size'],
        batch_size=config['batch_size'],
        random_state=config['random_state']
    )

    # Initialize feature extractor
    feature_extractor = VideoFeatureExtractor(
        model_name=config['model_name'],
        device=config['device'],
        dtype=config['dtype'],
        temporal_window=config['temporal_window']
    )

    # Initialize regression model
    regression_model = GaitRegressionModel(
        input_channels=16,  # Cosmos tokenizer output channels
        num_gait_params=len(param_names),
        hidden_dims=[512, 256, 128]
    )

    # Initialize trainer
    trainer = GaitRegressionTrainer(
        model=regression_model,
        feature_extractor=feature_extractor,
        device=config['device'],
        learning_rate=config['learning_rate'],
        weight_decay=config['weight_decay']
    )

    # Train the model
    history = trainer.fit(
        train_loader=train_loader,
        val_loader=val_loader,
        epochs=config['epochs'],
        save_path="best_gait_regression_model.pth"
    )

    # Plot training history
    plot_training_history(history, "training_history.png")

    # Load best model for evaluation
    checkpoint = torch.load("best_gait_regression_model.pth")
    regression_model.load_state_dict(checkpoint['model_state_dict'])

    # Evaluate on test set
    metrics = evaluate_model(
        model=regression_model,
        feature_extractor=feature_extractor,
        test_loader=test_loader,
        param_names=param_names,
        device=config['device'],
        save_path="evaluation_results.png"
    )

    logger.info("Pipeline completed successfully!")
    return metrics


if __name__ == "__main__":
    # Test data loading first
    try:
        video_paths, gait_params, param_names = load_and_preprocess_data()
        print(f"Loaded {len(video_paths)} videos with {gait_params.shape[1]} gait parameters")
        print(f"Parameter names: {param_names}")
        print(f"Gait parameters shape: {gait_params.shape}")

        # Run full pipeline if data loading is successful
        if len(video_paths) > 0:
            print("\nStarting full training pipeline...")
            metrics = main()
        else:
            print("No valid video data found. Please check your data directory.")

    except Exception as e:
        logger.error(f"Error in pipeline: {e}")
        import traceback
        traceback.print_exc()
