# Gait Parameter Regression using Cosmos Video Tokenizer

This project implements a deep learning regression model that predicts gait parameters from video data using NVIDIA's Cosmos Video Tokenizer as a feature extractor.

## Overview

The model leverages the powerful video representation capabilities of the Cosmos Tokenizer to extract meaningful features from gait videos and predict key gait parameters such as velocity, cadence, stride length, and swing percentages.

## Architecture

### 1. Video Feature Extraction
- **Cosmos Video Tokenizer**: Uses the pre-trained CV8x8x8 model to encode videos into compact latent representations
- **Input**: Videos in format (T, H, W, 3) with range [0, 255]
- **Output**: Feature tensors of shape (16, t, h, w) where compression is 8x8x8

### 2. Regression Model
- **Global Average Pooling**: Reduces spatial and temporal dimensions
- **Fully Connected Layers**: Multi-layer perceptron with dropout and batch normalization
- **Output**: Predicts 6 gait parameters simultaneously

### 3. Training Pipeline
- **Data Split**: 70% training, 10% validation, 20% testing
- **Loss Function**: Mean Squared Error (MSE)
- **Optimizer**: <PERSON> with learning rate 1e-4
- **Regularization**: Dropout (0.3) and weight decay (1e-5)

## Dataset

The model is trained on the GAITRite dataset containing:
- **Videos**: 17,901 gait video samples in .avi format
- **Parameters**: 6 key gait parameters per video
  - Velocity (cm/s)
  - Cadence (steps/min)
  - Stride Length Left/Right (cm)
  - Swing Percentage Left/Right (%)

## Installation

### Prerequisites
```bash
# Install Cosmos Tokenizer
git clone https://github.com/NVIDIA/Cosmos-Tokenizer.git
cd Cosmos-Tokenizer
pip install -e .

# Download pretrained models (requires Hugging Face token)
python -c "
from huggingface_hub import snapshot_download
import os
os.makedirs('pretrained_ckpts/Cosmos-1.0-Tokenizer-CV8x8x8', exist_ok=True)
snapshot_download(
    repo_id='nvidia/Cosmos-1.0-Tokenizer-CV8x8x8',
    local_dir='pretrained_ckpts/Cosmos-1.0-Tokenizer-CV8x8x8'
)
"
```

### Dependencies
```bash
pip install torch torchvision torchaudio
pip install pandas numpy scikit-learn matplotlib seaborn
pip install tqdm mediapy opencv-python
```

## Usage

### Basic Training
```python
from gait_regression_model import main

# Run the complete training pipeline
metrics = main()
```

### Custom Configuration
```python
from gait_regression_model import (
    load_and_preprocess_data, create_data_loaders,
    VideoFeatureExtractor, GaitRegressionModel, GaitRegressionTrainer
)

# Load data
video_paths, gait_params, param_names = load_and_preprocess_data()

# Create data loaders
train_loader, val_loader, test_loader = create_data_loaders(
    video_paths, gait_params, batch_size=4
)

# Initialize models
feature_extractor = VideoFeatureExtractor(
    model_name='Cosmos-1.0-Tokenizer-CV8x8x8',
    device='cuda'
)

regression_model = GaitRegressionModel(
    input_channels=16,
    num_gait_params=6,
    hidden_dims=[512, 256, 128]
)

# Train
trainer = GaitRegressionTrainer(regression_model, feature_extractor)
history = trainer.fit(train_loader, val_loader, epochs=30)
```

### Evaluation
```python
from gait_regression_model import evaluate_model

# Evaluate trained model
metrics = evaluate_model(
    model=regression_model,
    feature_extractor=feature_extractor,
    test_loader=test_loader,
    param_names=param_names
)
```

## File Structure

```
├── gait_regression_model.py    # Main model implementation
├── test_gait_regression.py     # Comprehensive test suite
├── README_gait_regression.md   # This documentation
├── data/
│   ├── gaitrite_full_dataset.xlsx
│   └── videos/
│       └── video_formated_trim/
└── pretrained_ckpts/
    └── Cosmos-1.0-Tokenizer-CV8x8x8/
        ├── encoder.jit
        └── decoder.jit
```

## Key Features

### 1. Robust Data Pipeline
- Automatic video-parameter matching
- Missing data handling
- Train/validation/test splitting
- Batch processing with memory optimization

### 2. Advanced Feature Extraction
- Temporal window processing for long videos
- Automatic padding and cropping
- GPU acceleration with mixed precision

### 3. Comprehensive Evaluation
- Per-parameter and overall metrics (R², MAE, MSE)
- Visualization of predictions vs ground truth
- Training history plots
- Model performance analysis

### 4. Production Ready
- Comprehensive error handling
- Logging and monitoring
- Model checkpointing
- Extensive unit and integration tests

## Performance Expectations

Based on the dataset characteristics:
- **Training Time**: ~2-4 hours on RTX 3080 (30 epochs)
- **Memory Usage**: ~8-12 GB GPU memory (batch size 2-4)
- **Inference Speed**: ~50-100ms per video
- **Expected R² Score**: 0.7-0.9 for well-correlated parameters

## Testing

Run the comprehensive test suite:
```bash
python test_gait_regression.py
```

Tests include:
- Unit tests for all components
- Integration tests for the complete pipeline
- Performance benchmarks
- Memory usage validation

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**
   - Reduce batch size to 1-2
   - Use smaller temporal windows
   - Enable gradient checkpointing

2. **Missing Video Files**
   - Check video directory path
   - Verify file naming convention
   - Ensure .avi format compatibility

3. **Poor Model Performance**
   - Increase training epochs
   - Adjust learning rate
   - Check data quality and preprocessing

### Performance Optimization

1. **Memory Optimization**
   - Use mixed precision training (bfloat16)
   - Process videos in smaller temporal windows
   - Clear GPU cache between batches

2. **Speed Optimization**
   - Use DataLoader with multiple workers
   - Pre-extract and cache features
   - Use TensorRT for inference

## Future Improvements

1. **Model Architecture**
   - Experiment with transformer-based architectures
   - Add attention mechanisms for temporal modeling
   - Multi-scale feature fusion

2. **Data Augmentation**
   - Temporal augmentation (speed variation)
   - Spatial augmentation (cropping, rotation)
   - Synthetic data generation

3. **Multi-task Learning**
   - Joint prediction of multiple gait aspects
   - Auxiliary tasks for better representation learning
   - Domain adaptation for different populations

## Citation

If you use this code in your research, please cite:

```bibtex
@software{gait_regression_cosmos,
  title={Gait Parameter Regression using Cosmos Video Tokenizer},
  author={Your Name},
  year={2024},
  url={https://github.com/your-repo/gait-regression-cosmos}
}
```

## License

This project is licensed under the Apache 2.0 License - see the LICENSE file for details.

## Acknowledgments

- NVIDIA for the Cosmos Video Tokenizer
- GAITRite dataset contributors
- PyTorch and scikit-learn communities
